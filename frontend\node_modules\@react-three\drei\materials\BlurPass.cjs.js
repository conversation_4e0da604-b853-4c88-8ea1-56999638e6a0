"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("three"),t=require("./ConvolutionMaterial.cjs.js");require("../helpers/constants.cjs.js");exports.BlurPass=class{constructor({gl:r,resolution:i,width:n=500,height:s=500,minDepthThreshold:o=0,maxDepthThreshold:a=1,depthScale:l=0,depthToBlurRatioBias:u=.25}){this.renderToScreen=!1,this.renderTargetA=new e.WebGLRenderTarget(i,i,{minFilter:e.LinearFilter,magFilter:e.LinearFilter,stencilBuffer:!1,depthBuffer:!1,type:e.HalfFloatType}),this.renderTargetB=this.renderTargetA.clone(),this.convolutionMaterial=new t.ConvolutionMaterial,this.convolutionMaterial.setTexelSize(1/n,1/s),this.convolutionMaterial.setResolution(new e.Vector2(n,s)),this.scene=new e.Scene,this.camera=new e.Camera,this.convolutionMaterial.uniforms.minDepthThreshold.value=o,this.convolutionMaterial.uniforms.maxDepthThreshold.value=a,this.convolutionMaterial.uniforms.depthScale.value=l,this.convolutionMaterial.uniforms.depthToBlurRatioBias.value=u,this.convolutionMaterial.defines.USE_DEPTH=l>0;const h=new Float32Array([-1,-1,0,3,-1,0,-1,3,0]),c=new Float32Array([0,0,2,0,0,2]),d=new e.BufferGeometry;d.setAttribute("position",new e.BufferAttribute(h,3)),d.setAttribute("uv",new e.BufferAttribute(c,2)),this.screen=new e.Mesh(d,this.convolutionMaterial),this.screen.frustumCulled=!1,this.scene.add(this.screen)}render(e,t,r){const i=this.scene,n=this.camera,s=this.renderTargetA,o=this.renderTargetB;let a=this.convolutionMaterial,l=a.uniforms;l.depthBuffer.value=t.depthTexture;const u=a.kernel;let h,c,d,f=t;for(c=0,d=u.length-1;c<d;++c)h=1&c?o:s,l.kernel.value=u[c],l.inputBuffer.value=f.texture,e.setRenderTarget(h),e.render(i,n),f=h;l.kernel.value=u[c],l.inputBuffer.value=f.texture,e.setRenderTarget(this.renderToScreen?null:r),e.render(i,n)}};
