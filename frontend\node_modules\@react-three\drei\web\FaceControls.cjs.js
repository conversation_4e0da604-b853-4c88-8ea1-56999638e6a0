"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("three"),t=require("react"),o=require("@react-three/fiber"),n=require("maath"),c=require("../core/VideoTexture.cjs.js"),a=require("./WebcamVideoTexture.cjs.js"),u=require("./Facemesh.cjs.js"),s=require("./FaceLandmarker.cjs.js");function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function l(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var o=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,o.get?o:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}require("suspend-react"),require("hls.js"),require("../core/Line.cjs.js"),require("three-stdlib");var f=i(e),d=l(r),p=l(t);function m(e,r){return e.clone().add(r).multiplyScalar(.5)}function b(e,r,t){const o=e.localToWorld(r);return t.worldToLocal(o)}const h=t.createContext({}),y=t.forwardRef((({camera:e,videoTexture:r={start:!0},manualDetect:i=!1,faceLandmarkerResult:l,manualUpdate:y=!1,makeDefault:j,smoothTime:v=.25,offset:T=!0,offsetScalar:V=80,eyes:w=!1,eyesAsOrigin:g=!0,depth:q=.15,debug:x=!1,facemesh:O},k)=>{var S,R;const E=o.useThree((e=>e.scene)),D=o.useThree((e=>e.camera)),F=o.useThree((e=>e.set)),C=o.useThree((e=>e.get)),L=e||D,M=t.useRef(null),[A]=t.useState((()=>new d.Object3D)),[P]=t.useState((()=>new d.Vector3)),[W]=t.useState((()=>new d.Vector3)),[_]=t.useState((()=>new d.Vector3)),[z]=t.useState((()=>new d.Vector3)),B=t.useCallback((()=>{A.parent=L.parent;const e=M.current;if(e){const{outerRef:r,eyeRightRef:t,eyeLeftRef:o}=e;if(t.current&&o.current){const{irisDirRef:e}=t.current,{irisDirRef:n}=o.current;e.current&&n.current&&r.current&&(P.copy(b(e.current,new d.Vector3(0,0,0),r.current)),W.copy(b(n.current,new d.Vector3(0,0,0),r.current)),A.position.copy(b(r.current,m(P,W),L.parent||E)),_.copy(b(e.current,new d.Vector3(0,0,1),r.current)),z.copy(b(n.current,new d.Vector3(0,0,1),r.current)),A.lookAt(r.current.localToWorld(m(_,z))))}else r.current&&(A.position.copy(b(r.current,new d.Vector3(0,0,0),L.parent||E)),A.lookAt(r.current.localToWorld(new d.Vector3(0,0,1))))}return A}),[L,W,z,P,_,E,A]),[I]=t.useState((()=>new d.Object3D)),H=t.useCallback((function(e,r){if(L){var t;if(null!==(t=r)&&void 0!==t||(r=B()),v>0){const t=1e-9;n.easing.damp3(I.position,r.position,v,e,void 0,void 0,t),n.easing.dampE(I.rotation,r.rotation,v,e,void 0,void 0,t)}else I.position.copy(r.position),I.rotation.copy(r.rotation);L.position.copy(I.position),L.rotation.copy(I.rotation)}}),[L,B,v,I.position,I.rotation]);o.useFrame(((e,r)=>{y||H(r)}));const N=t.useRef(null),[U,G]=t.useState(),J=s.useFaceLandmarker(),K=t.useCallback(((e,r)=>{const t=N.current;if(!t)return;const o=t.source.data,n=null==J?void 0:J.detectForVideo(o,e);G(n)}),[J]),Q=t.useMemo((()=>Object.assign(Object.create(d.EventDispatcher.prototype),{computeTarget:B,update:H,facemeshApiRef:M})),[B,H]);t.useImperativeHandle(k,(()=>Q),[Q]),t.useEffect((()=>{if(j){const e=C().controls;return F({controls:Q}),()=>F({controls:e})}}),[j,Q,C,F]);const X=null!=l?l:U,Y=null==X?void 0:X.faceLandmarks[0],Z=null==X||null==(S=X.facialTransformationMatrixes)?void 0:S[0],$=null==X||null==(R=X.faceBlendshapes)?void 0:R[0],ee={onVideoFrame:K,...r};return p.createElement(h.Provider,{value:Q},!i&&p.createElement(t.Suspense,{fallback:null},"src"in ee?p.createElement(c.VideoTexture,f.default({ref:N},ee)):p.createElement(a.WebcamVideoTexture,f.default({ref:N},ee))),p.createElement(u.Facemesh,f.default({ref:M,children:p.createElement("meshNormalMaterial",{side:d.DoubleSide})},O,{points:Y,depth:q,facialTransformationMatrix:Z,faceBlendshapes:$,eyes:w,eyesAsOrigin:g,offset:T,offsetScalar:V,debug:x,"rotation-z":Math.PI,visible:x})))}));exports.FaceControls=y,exports.useFaceControls=()=>t.useContext(h);
