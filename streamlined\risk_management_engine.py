"""
A.T.L.A.S AI-Enhanced Risk Management Engine
Position sizing calculator with Kelly Criterion optimization and safety guardrails
"""

import logging
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
from dataclasses import dataclass

from .config import settings
from .models import (
    RiskManagementProfile, Position, Quote, TechnicalIndicators,
    ChainOfThoughtAnalysis
)


@dataclass
class PositionSizeCalculation:
    """Result of position sizing calculation"""
    recommended_shares: int
    dollar_amount: float
    risk_amount: float
    stop_loss_price: float
    target_price: float
    risk_reward_ratio: float
    kelly_fraction_used: float
    confidence_adjustment: float
    warnings: List[str]
    educational_explanation: str


@dataclass
class PreTradeValidation:
    """Pre-trade validation result"""
    is_valid: bool
    confidence_score: float
    risk_score: float
    warnings: List[str]
    blockers: List[str]
    educational_notes: List[str]
    recommendation: str


class RiskManagementEngine:
    """
    Comprehensive risk management system with beginner-friendly explanations
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Risk management constants
        self.MAX_POSITION_SIZE_PERCENT = 0.20  # 20% max position size
        self.MAX_DAILY_RISK_PERCENT = 0.03     # 3% max daily risk
        self.MAX_CORRELATION = 0.85            # 85% max correlation between positions
        self.MIN_LIQUIDITY_VOLUME = 100000     # Minimum daily volume
        self.VIX_DANGER_THRESHOLD = 40.0       # VIX level to pause trading
        
        # Kelly Criterion parameters
        self.KELLY_SAFETY_FACTOR = 0.5  # Use half Kelly for safety
        self.MIN_KELLY_FRACTION = 0.01  # 1% minimum position
        self.MAX_KELLY_FRACTION = 0.25  # 25% maximum position
    
    def calculate_position_size(self, 
                              symbol: str,
                              entry_price: float,
                              account_size: float,
                              confidence: float,
                              risk_profile: RiskManagementProfile,
                              technical_indicators: Optional[TechnicalIndicators] = None) -> PositionSizeCalculation:
        """
        Calculate optimal position size using Kelly Criterion with safety adjustments
        """
        
        warnings = []
        
        # Calculate stop loss using ATR or percentage method
        stop_loss_price, stop_method = self._calculate_stop_loss(
            entry_price, technical_indicators
        )
        
        # Calculate target price (2:1 risk/reward minimum)
        risk_per_share = entry_price - stop_loss_price
        target_price = entry_price + (risk_per_share * 2.0)
        
        # Kelly Criterion calculation
        win_rate = 0.65  # Historical TTM Squeeze win rate
        avg_win = 0.08   # 8% average win
        avg_loss = abs(risk_per_share / entry_price)  # Actual risk percentage
        
        # Kelly formula: f = (bp - q) / b
        b = avg_win / avg_loss if avg_loss > 0 else 2.0
        p = win_rate
        q = 1 - win_rate
        
        kelly_fraction = (b * p - q) / b if b > 0 else 0.05
        
        # Apply safety factor and confidence adjustment
        confidence_adjustment = confidence * 0.8 + 0.2  # Scale 0.2-1.0
        adjusted_kelly = kelly_fraction * self.KELLY_SAFETY_FACTOR * confidence_adjustment
        
        # Clamp Kelly fraction to safe limits
        adjusted_kelly = max(self.MIN_KELLY_FRACTION, 
                           min(adjusted_kelly, self.MAX_KELLY_FRACTION))
        
        # Calculate position size
        max_risk_amount = account_size * (risk_profile.daily_loss_limit_percent / 100) * 0.4
        kelly_risk_amount = account_size * adjusted_kelly
        
        # Use smaller of Kelly or max risk
        risk_amount = min(kelly_risk_amount, max_risk_amount)
        
        # Calculate shares based on risk per share
        if risk_per_share > 0:
            recommended_shares = int(risk_amount / risk_per_share)
        else:
            recommended_shares = 0
            warnings.append("Unable to calculate stop loss - position sizing not possible")
        
        # Position size validations
        dollar_amount = recommended_shares * entry_price
        position_percent = (dollar_amount / account_size) * 100
        
        if position_percent > self.MAX_POSITION_SIZE_PERCENT * 100:
            # Reduce position size
            max_dollar_amount = account_size * self.MAX_POSITION_SIZE_PERCENT
            recommended_shares = int(max_dollar_amount / entry_price)
            dollar_amount = recommended_shares * entry_price
            warnings.append(f"Position size reduced to {self.MAX_POSITION_SIZE_PERCENT*100:.0f}% of account for safety")
        
        # Risk/reward ratio
        risk_reward_ratio = abs((target_price - entry_price) / (entry_price - stop_loss_price)) if risk_per_share > 0 else 0
        
        # Generate educational explanation
        educational_explanation = self._generate_position_sizing_explanation(
            recommended_shares, dollar_amount, risk_amount, kelly_fraction, 
            confidence_adjustment, stop_method, risk_reward_ratio
        )
        
        return PositionSizeCalculation(
            recommended_shares=recommended_shares,
            dollar_amount=dollar_amount,
            risk_amount=risk_amount,
            stop_loss_price=stop_loss_price,
            target_price=target_price,
            risk_reward_ratio=risk_reward_ratio,
            kelly_fraction_used=adjusted_kelly,
            confidence_adjustment=confidence_adjustment,
            warnings=warnings,
            educational_explanation=educational_explanation
        )
    
    def _calculate_stop_loss(self, entry_price: float, 
                           indicators: Optional[TechnicalIndicators]) -> Tuple[float, str]:
        """Calculate stop loss price using ATR or percentage method"""
        
        if indicators and indicators.atr:
            # ATR-based stop loss (1.5x ATR)
            atr_stop = entry_price - (indicators.atr * 1.5)
            method = "ATR-based"
            return max(atr_stop, entry_price * 0.97), method  # Minimum 3% stop
        else:
            # Percentage-based stop loss (3%)
            percentage_stop = entry_price * 0.97
            method = "Percentage-based"
            return percentage_stop, method
    
    def validate_trade_setup(self, 
                           symbol: str,
                           entry_price: float,
                           position_size: PositionSizeCalculation,
                           current_positions: List[Position],
                           cot_analysis: Optional[ChainOfThoughtAnalysis] = None,
                           market_conditions: Optional[Dict[str, Any]] = None) -> PreTradeValidation:
        """
        Comprehensive pre-trade validation with educational warnings
        """
        
        warnings = []
        blockers = []
        educational_notes = []
        
        # Confidence validation
        confidence_score = cot_analysis.final_confidence if cot_analysis else 0.5
        if confidence_score < 0.70:
            warnings.append(f"Low confidence signal ({confidence_score*100:.0f}%) - consider reducing position size")
            educational_notes.append("💡 High-confidence trades (70%+) have better success rates historically")
        
        # Risk validation
        risk_score = 1.0
        
        # Check daily risk limits
        total_daily_risk = sum([pos.unrealized_pl for pos in current_positions if pos.unrealized_pl < 0])
        total_daily_risk += position_size.risk_amount
        
        account_size = sum([abs(pos.market_value) for pos in current_positions]) + 50000  # Estimate
        daily_risk_percent = abs(total_daily_risk / account_size) * 100
        
        if daily_risk_percent > self.MAX_DAILY_RISK_PERCENT * 100:
            blockers.append(f"Daily risk limit exceeded: {daily_risk_percent:.1f}% > {self.MAX_DAILY_RISK_PERCENT*100:.1f}%")
            risk_score -= 0.4
        
        # Position concentration check
        position_percent = (position_size.dollar_amount / account_size) * 100
        if position_percent > 15:
            warnings.append(f"Large position size: {position_percent:.1f}% of account")
            educational_notes.append("⚠️ Large positions increase risk - consider diversifying across multiple trades")
            risk_score -= 0.2
        
        # Sector diversification check (simplified)
        sector_exposure = self._estimate_sector_exposure(symbol, current_positions)
        if sector_exposure > 0.4:  # 40% sector limit
            warnings.append(f"High sector concentration: {sector_exposure*100:.0f}%")
            educational_notes.append("🏭 Diversify across sectors to reduce correlation risk")
            risk_score -= 0.2
        
        # Market timing checks
        current_hour = datetime.now().hour
        if current_hour < 10 or current_hour > 15:
            warnings.append("Trading outside main market hours - reduced liquidity")
            educational_notes.append("⏰ Best liquidity typically between 10 AM - 3 PM EST")
        
        # Volatility check (VIX simulation)
        if market_conditions and market_conditions.get("vix", 20) > self.VIX_DANGER_THRESHOLD:
            blockers.append(f"High market volatility detected - trading suspended for safety")
            educational_notes.append("🌪️ High VIX indicates market stress - wait for calmer conditions")
            risk_score -= 0.5
        
        # Risk/reward validation
        if position_size.risk_reward_ratio < 1.5:
            warnings.append(f"Poor risk/reward ratio: {position_size.risk_reward_ratio:.1f}:1")
            educational_notes.append("📊 Aim for at least 2:1 risk/reward ratio for profitable trading")
            risk_score -= 0.1
        
        # Generate final recommendation
        is_valid = len(blockers) == 0
        
        if is_valid and confidence_score >= 0.80 and risk_score >= 0.8:
            recommendation = "STRONG GO - Excellent setup with high confidence and low risk"
        elif is_valid and confidence_score >= 0.65 and risk_score >= 0.6:
            recommendation = "GO - Good setup, proceed with recommended position size"
        elif is_valid and len(warnings) <= 2:
            recommendation = "CAUTION - Proceed with reduced position size"
        elif is_valid:
            recommendation = "HIGH CAUTION - Multiple warnings, consider waiting"
        else:
            recommendation = "NO GO - Critical issues must be resolved first"
        
        return PreTradeValidation(
            is_valid=is_valid,
            confidence_score=confidence_score,
            risk_score=max(risk_score, 0.0),
            warnings=warnings,
            blockers=blockers,
            educational_notes=educational_notes,
            recommendation=recommendation
        )
    
    def _estimate_sector_exposure(self, symbol: str, positions: List[Position]) -> float:
        """Estimate sector exposure (simplified sector mapping)"""
        
        # Simplified sector mapping
        tech_stocks = ["AAPL", "MSFT", "GOOGL", "AMZN", "META", "NVDA", "NFLX", "ADBE", "CRM", "INTC"]
        finance_stocks = ["JPM", "BAC", "WFC", "GS", "MS", "C", "USB", "PNC", "TFC", "COF"]
        healthcare_stocks = ["JNJ", "UNH", "PFE", "ABBV", "TMO", "ABT", "DHR", "BMY", "AMGN", "GILD"]
        
        current_symbol_sector = None
        if symbol in tech_stocks:
            current_symbol_sector = "tech"
        elif symbol in finance_stocks:
            current_symbol_sector = "finance"
        elif symbol in healthcare_stocks:
            current_symbol_sector = "healthcare"
        
        if not current_symbol_sector:
            return 0.0  # Unknown sector, assume no concentration
        
        # Calculate current sector exposure
        total_value = sum([abs(pos.market_value) for pos in positions])
        sector_value = sum([abs(pos.market_value) for pos in positions 
                           if self._get_symbol_sector(pos.symbol) == current_symbol_sector])
        
        return sector_value / total_value if total_value > 0 else 0.0
    
    def _get_symbol_sector(self, symbol: str) -> Optional[str]:
        """Get sector for symbol (simplified)"""
        tech_stocks = ["AAPL", "MSFT", "GOOGL", "AMZN", "META", "NVDA", "NFLX", "ADBE", "CRM", "INTC"]
        finance_stocks = ["JPM", "BAC", "WFC", "GS", "MS", "C", "USB", "PNC", "TFC", "COF"]
        healthcare_stocks = ["JNJ", "UNH", "PFE", "ABBV", "TMO", "ABT", "DHR", "BMY", "AMGN", "GILD"]
        
        if symbol in tech_stocks:
            return "tech"
        elif symbol in finance_stocks:
            return "finance"
        elif symbol in healthcare_stocks:
            return "healthcare"
        return None
    
    def _generate_position_sizing_explanation(self, shares: int, dollar_amount: float,
                                            risk_amount: float, kelly_fraction: float,
                                            confidence_adj: float, stop_method: str,
                                            risk_reward: float) -> str:
        """Generate beginner-friendly position sizing explanation"""
        
        explanation = f"""
🧮 POSITION SIZING CALCULATION EXPLAINED

Recommended Position: {shares} shares (${dollar_amount:,.2f})
Risk Amount: ${risk_amount:.2f}

📚 HOW WE CALCULATED THIS:

1️⃣ KELLY CRITERION (Mathematical Optimization):
   • Base Kelly Fraction: {kelly_fraction:.1%}
   • Confidence Adjustment: {confidence_adj:.1%}
   • This formula tells us the mathematically optimal bet size to grow your account safely

2️⃣ STOP LOSS CALCULATION:
   • Method: {stop_method}
   • This is your "safety net" - the price where you'll exit to limit losses

3️⃣ RISK/REWARD RATIO:
   • Current Ratio: {risk_reward:.1f}:1
   • For every $1 you risk, you aim to make ${risk_reward:.1f}

💡 BEGINNER EXPLANATION:
Think of position sizing like deciding how much to bet in a casino game where you have an edge.
The Kelly Criterion is like having a mathematical advisor telling you the optimal bet size
to maximize long-term growth while minimizing the chance of going broke.

We're being extra conservative by using only half the Kelly recommendation for safety!

⚠️ REMEMBER: Never risk more than you can afford to lose, and always use stop losses!
        """.strip()
        
        return explanation
