"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("../core/Billboard.cjs.js"),r=require("../core/ScreenSpace.cjs.js"),s=require("../core/ScreenSizer.cjs.js"),o=require("../core/QuadraticBezierLine.cjs.js"),t=require("../core/CubicBezierLine.cjs.js"),i=require("../core/CatmullRomLine.cjs.js"),c=require("../core/Line.cjs.js"),a=require("../core/PositionalAudio.cjs.js"),u=require("../core/Text.cjs.js"),n=require("../core/Text3D.cjs.js"),p=require("../core/Effects.cjs.js"),j=require("../core/GradientTexture.cjs.js"),x=require("../core/Image.cjs.js"),l=require("../core/Edges.cjs.js"),d=require("../core/Outlines.cjs.js"),q=require("../core/Trail.cjs.js"),m=require("../core/Sampler.cjs.js"),h=require("../core/ComputedAttribute.cjs.js"),C=require("../core/Clone.cjs.js"),M=require("../core/MarchingCubes.cjs.js"),S=require("../core/Decal.cjs.js"),T=require("../core/Svg.cjs.js"),b=require("../core/Gltf.cjs.js"),P=require("../core/AsciiRenderer.cjs.js"),f=require("../core/Splat.cjs.js"),g=require("../core/OrthographicCamera.cjs.js"),B=require("../core/PerspectiveCamera.cjs.js"),v=require("../core/CubeCamera.cjs.js"),A=require("../core/DeviceOrientationControls.cjs.js"),F=require("../core/FlyControls.cjs.js"),L=require("../core/MapControls.cjs.js"),G=require("../core/OrbitControls.cjs.js"),R=require("../core/TrackballControls.cjs.js"),D=require("../core/ArcballControls.cjs.js"),E=require("../core/TransformControls.cjs.js"),k=require("../core/PointerLockControls.cjs.js"),I=require("../core/FirstPersonControls.cjs.js"),w=require("../core/CameraControls.cjs.js"),z=require("../core/MotionPathControls.cjs.js"),O=require("../core/GizmoHelper.cjs.js"),y=require("../core/GizmoViewcube.cjs.js"),H=require("../core/GizmoViewport.cjs.js"),V=require("../core/Grid.cjs.js"),W=require("../core/CubeTexture.cjs.js"),K=require("../core/Fbx.cjs.js"),Q=require("../core/Ktx2.cjs.js"),N=require("../core/Progress.cjs.js"),U=require("../core/Texture.cjs.js"),X=require("../core/VideoTexture.cjs.js"),_=require("../core/useFont.cjs.js"),J=require("../core/useSpriteLoader.cjs.js"),Y=require("../core/Helper.cjs.js"),Z=require("../core/Stats.cjs.js"),$=require("../core/StatsGl.cjs.js"),ee=require("../core/useDepthBuffer.cjs.js"),re=require("../core/useAspect.cjs.js"),se=require("../core/useCamera.cjs.js"),oe=require("../core/DetectGPU.cjs.js"),te=require("../core/Bvh.cjs.js"),ie=require("../core/useContextBridge.cjs.js"),ce=require("../core/useAnimations.cjs.js"),ae=require("../core/Fbo.cjs.js"),ue=require("../core/useIntersect.cjs.js"),ne=require("../core/useBoxProjectedEnv.cjs.js"),pe=require("../core/BBAnchor.cjs.js"),je=require("../core/TrailTexture.cjs.js"),xe=require("../core/Example.cjs.js"),le=require("../core/SpriteAnimator.cjs.js"),de=require("../core/CurveModifier.cjs.js"),qe=require("../core/MeshDistortMaterial.cjs.js"),me=require("../core/MeshWobbleMaterial.cjs.js"),he=require("../core/MeshReflectorMaterial.cjs.js"),Ce=require("../core/MeshRefractionMaterial.cjs.js"),Me=require("../core/MeshTransmissionMaterial.cjs.js"),Se=require("../core/MeshDiscardMaterial.cjs.js"),Te=require("../core/MultiMaterial.cjs.js"),be=require("../core/PointMaterial.cjs.js"),Pe=require("../core/shaderMaterial.cjs.js"),fe=require("../core/softShadows.cjs.js"),ge=require("../core/shapes.cjs.js"),Be=require("../core/RoundedBox.cjs.js"),ve=require("../core/ScreenQuad.cjs.js"),Ae=require("../core/Center.cjs.js"),Fe=require("../core/Resize.cjs.js"),Le=require("../core/Bounds.cjs.js"),Ge=require("../core/CameraShake.cjs.js"),Re=require("../core/Float.cjs.js"),De=require("../core/Stage.cjs.js"),Ee=require("../core/Backdrop.cjs.js"),ke=require("../core/Shadow.cjs.js"),Ie=require("../core/Caustics.cjs.js"),we=require("../core/ContactShadows.cjs.js"),ze=require("../core/AccumulativeShadows.cjs.js"),Oe=require("../core/Reflector.cjs.js"),ye=require("../core/SpotLight.cjs.js"),He=require("../core/Environment.cjs.js"),Ve=require("../core/Lightformer.cjs.js"),We=require("../core/Sky.cjs.js"),Ke=require("../core/Stars.cjs.js"),Qe=require("../core/Cloud.cjs.js"),Ne=require("../core/Sparkles.cjs.js"),Ue=require("../core/useEnvironment.cjs.js"),Xe=require("../core/MatcapTexture.cjs.js"),_e=require("../core/NormalTexture.cjs.js"),Je=require("../core/Wireframe.cjs.js"),Ye=require("../core/ShadowAlpha.cjs.js"),Ze=require("../core/Points.cjs.js"),$e=require("../core/Instances.cjs.js"),er=require("../core/Segments.cjs.js"),rr=require("../core/Detailed.cjs.js"),sr=require("../core/Preload.cjs.js"),or=require("../core/BakeShadows.cjs.js"),tr=require("../core/meshBounds.cjs.js"),ir=require("../core/AdaptiveDpr.cjs.js"),cr=require("../core/AdaptiveEvents.cjs.js"),ar=require("../core/PerformanceMonitor.cjs.js"),ur=require("../core/RenderTexture.cjs.js"),nr=require("../core/RenderCubeTexture.cjs.js"),pr=require("../core/Mask.cjs.js"),jr=require("../core/Hud.cjs.js"),xr=require("../core/Fisheye.cjs.js"),lr=require("../core/MeshPortalMaterial.cjs.js"),dr=require("../core/calculateScaleFactor.cjs.js");require("@babel/runtime/helpers/extends"),require("@react-three/fiber"),require("react"),require("three"),require("three-stdlib"),require("troika-three-text"),require("suspend-react"),require("../helpers/constants.cjs.js"),require("meshline"),require("camera-controls"),require("maath"),require("zustand"),require("hls.js"),require("stats.js"),require("../helpers/useEffectfulState.cjs.js"),require("stats-gl"),require("detect-gpu"),require("three-mesh-bvh"),require("react-composer"),require("../helpers/deprecated.cjs.js"),require("../materials/BlurPass.cjs.js"),require("../materials/ConvolutionMaterial.cjs.js"),require("../materials/MeshReflectorMaterial.cjs.js"),require("../materials/MeshRefractionMaterial.cjs.js"),require("../materials/DiscardMaterial.cjs.js"),require("@monogrid/gainmap-js"),require("../helpers/environment-assets.cjs.js"),require("../materials/SpotLightMaterial.cjs.js"),require("../materials/WireframeMaterial.cjs.js"),exports.Billboard=e.Billboard,exports.ScreenSpace=r.ScreenSpace,exports.ScreenSizer=s.ScreenSizer,exports.QuadraticBezierLine=o.QuadraticBezierLine,exports.CubicBezierLine=t.CubicBezierLine,exports.CatmullRomLine=i.CatmullRomLine,exports.Line=c.Line,exports.PositionalAudio=a.PositionalAudio,exports.Text=u.Text,exports.Text3D=n.Text3D,exports.Effects=p.Effects,exports.isWebGL2Available=p.isWebGL2Available,exports.GradientTexture=j.GradientTexture,exports.GradientType=j.GradientType,exports.Image=x.Image,exports.Edges=l.Edges,exports.Outlines=d.Outlines,exports.Trail=q.Trail,exports.useTrail=q.useTrail,exports.Sampler=m.Sampler,exports.useSurfaceSampler=m.useSurfaceSampler,exports.ComputedAttribute=h.ComputedAttribute,exports.Clone=C.Clone,exports.MarchingCube=M.MarchingCube,exports.MarchingCubes=M.MarchingCubes,exports.MarchingPlane=M.MarchingPlane,exports.Decal=S.Decal,exports.Svg=T.Svg,exports.Gltf=b.Gltf,exports.useGLTF=b.useGLTF,exports.AsciiRenderer=P.AsciiRenderer,exports.Splat=f.Splat,exports.OrthographicCamera=g.OrthographicCamera,exports.PerspectiveCamera=B.PerspectiveCamera,exports.CubeCamera=v.CubeCamera,exports.useCubeCamera=v.useCubeCamera,exports.DeviceOrientationControls=A.DeviceOrientationControls,exports.FlyControls=F.FlyControls,exports.MapControls=L.MapControls,exports.OrbitControls=G.OrbitControls,exports.TrackballControls=R.TrackballControls,exports.ArcballControls=D.ArcballControls,exports.TransformControls=E.TransformControls,exports.PointerLockControls=k.PointerLockControls,exports.FirstPersonControls=I.FirstPersonControls,exports.CameraControls=w.CameraControls,exports.MotionPathControls=z.MotionPathControls,exports.useMotion=z.useMotion,exports.GizmoHelper=O.GizmoHelper,exports.useGizmoContext=O.useGizmoContext,exports.GizmoViewcube=y.GizmoViewcube,exports.GizmoViewport=H.GizmoViewport,exports.Grid=V.Grid,exports.CubeTexture=W.CubeTexture,exports.useCubeTexture=W.useCubeTexture,exports.Fbx=K.Fbx,exports.useFBX=K.useFBX,exports.Ktx2=Q.Ktx2,exports.useKTX2=Q.useKTX2,exports.Progress=N.Progress,exports.useProgress=N.useProgress,exports.IsObject=U.IsObject,exports.Texture=U.Texture,exports.useTexture=U.useTexture,exports.VideoTexture=X.VideoTexture,exports.useVideoTexture=X.useVideoTexture,exports.useFont=_.useFont,exports.checkIfFrameIsEmpty=J.checkIfFrameIsEmpty,exports.getFirstFrame=J.getFirstFrame,exports.useSpriteLoader=J.useSpriteLoader,exports.Helper=Y.Helper,exports.useHelper=Y.useHelper,exports.Stats=Z.Stats,exports.StatsGl=$.StatsGl,exports.useDepthBuffer=ee.useDepthBuffer,exports.useAspect=re.useAspect,exports.useCamera=se.useCamera,exports.DetectGPU=oe.DetectGPU,exports.useDetectGPU=oe.useDetectGPU,exports.Bvh=te.Bvh,exports.useBVH=te.useBVH,exports.useContextBridge=ie.useContextBridge,exports.useAnimations=ce.useAnimations,exports.Fbo=ae.Fbo,exports.useFBO=ae.useFBO,exports.useIntersect=ue.useIntersect,exports.useBoxProjectedEnv=ne.useBoxProjectedEnv,exports.BBAnchor=pe.BBAnchor,exports.TrailTexture=je.TrailTexture,exports.useTrailTexture=je.useTrailTexture,exports.Example=xe.Example,exports.SpriteAnimator=le.SpriteAnimator,exports.useSpriteAnimator=le.useSpriteAnimator,exports.CurveModifier=de.CurveModifier,exports.MeshDistortMaterial=qe.MeshDistortMaterial,exports.MeshWobbleMaterial=me.MeshWobbleMaterial,exports.MeshReflectorMaterial=he.MeshReflectorMaterial,exports.MeshRefractionMaterial=Ce.MeshRefractionMaterial,exports.MeshTransmissionMaterial=Me.MeshTransmissionMaterial,exports.MeshDiscardMaterial=Se.MeshDiscardMaterial,exports.MultiMaterial=Te.MultiMaterial,exports.PointMaterial=be.PointMaterial,exports.PointMaterialImpl=be.PointMaterialImpl,exports.shaderMaterial=Pe.shaderMaterial,exports.SoftShadows=fe.SoftShadows,exports.Box=ge.Box,exports.Capsule=ge.Capsule,exports.Circle=ge.Circle,exports.Cone=ge.Cone,exports.Cylinder=ge.Cylinder,exports.Dodecahedron=ge.Dodecahedron,exports.Extrude=ge.Extrude,exports.Icosahedron=ge.Icosahedron,exports.Lathe=ge.Lathe,exports.Octahedron=ge.Octahedron,exports.Plane=ge.Plane,exports.Polyhedron=ge.Polyhedron,exports.Ring=ge.Ring,exports.Shape=ge.Shape,exports.Sphere=ge.Sphere,exports.Tetrahedron=ge.Tetrahedron,exports.Torus=ge.Torus,exports.TorusKnot=ge.TorusKnot,exports.Tube=ge.Tube,exports.RoundedBox=Be.RoundedBox,exports.ScreenQuad=ve.ScreenQuad,exports.Center=Ae.Center,exports.Resize=Fe.Resize,exports.Bounds=Le.Bounds,exports.useBounds=Le.useBounds,exports.CameraShake=Ge.CameraShake,exports.Float=Re.Float,exports.Stage=De.Stage,exports.Backdrop=Ee.Backdrop,exports.Shadow=ke.Shadow,exports.Caustics=Ie.Caustics,exports.ContactShadows=we.ContactShadows,exports.AccumulativeShadows=ze.AccumulativeShadows,exports.RandomizedLight=ze.RandomizedLight,exports.accumulativeContext=ze.accumulativeContext,exports.Reflector=Oe.Reflector,exports.SpotLight=ye.SpotLight,exports.SpotLightShadow=ye.SpotLightShadow,exports.Environment=He.Environment,exports.EnvironmentCube=He.EnvironmentCube,exports.EnvironmentMap=He.EnvironmentMap,exports.EnvironmentPortal=He.EnvironmentPortal,exports.Lightformer=Ve.Lightformer,exports.Sky=We.Sky,exports.calcPosFromAngles=We.calcPosFromAngles,exports.Stars=Ke.Stars,exports.Cloud=Qe.Cloud,exports.CloudInstance=Qe.CloudInstance,exports.Clouds=Qe.Clouds,exports.Sparkles=Ne.Sparkles,exports.useEnvironment=Ue.useEnvironment,exports.MatcapTexture=Xe.MatcapTexture,exports.useMatcapTexture=Xe.useMatcapTexture,exports.NormalTexture=_e.NormalTexture,exports.useNormalTexture=_e.useNormalTexture,exports.Wireframe=Je.Wireframe,exports.ShadowAlpha=Ye.ShadowAlpha,exports.Point=Ze.Point,exports.Points=Ze.Points,exports.PointsBuffer=Ze.PointsBuffer,exports.PositionPoint=Ze.PositionPoint,exports.Instance=$e.Instance,exports.InstancedAttribute=$e.InstancedAttribute,exports.Instances=$e.Instances,exports.Merged=$e.Merged,exports.PositionMesh=$e.PositionMesh,exports.createInstances=$e.createInstances,exports.Segment=er.Segment,exports.SegmentObject=er.SegmentObject,exports.Segments=er.Segments,exports.Detailed=rr.Detailed,exports.Preload=sr.Preload,exports.BakeShadows=or.BakeShadows,exports.meshBounds=tr.meshBounds,exports.AdaptiveDpr=ir.AdaptiveDpr,exports.AdaptiveEvents=cr.AdaptiveEvents,exports.PerformanceMonitor=ar.PerformanceMonitor,exports.usePerformanceMonitor=ar.usePerformanceMonitor,exports.RenderTexture=ur.RenderTexture,exports.RenderCubeTexture=nr.RenderCubeTexture,exports.Mask=pr.Mask,exports.useMask=pr.useMask,exports.Hud=jr.Hud,exports.Fisheye=xr.Fisheye,exports.MeshPortalMaterial=lr.MeshPortalMaterial,exports.calculateScaleFactor=dr.calculateScaleFactor;
