"""
A.T.L.A.S Chain-of-Thought Signal Analysis Engine
Provides transparent, beginner-friendly trading logic explanations
"""

import asyncio
import logging
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal

from .config import settings, TECHNICAL_PARAMS
from .models import (
    OHLCV, TechnicalIndicators, ChainOfThoughtStep, ChainOfThoughtAnalysis,
    ProfitTargetedStrategy, RiskManagementProfile, OptionsEducationContext
)
from .market_data import MarketDataService
from .technical_analysis import TechnicalAnalysisEngine


class ChainOfThoughtEngine:
    """
    Beginner-friendly Chain-of-Thought analysis engine that explains
    complex technical analysis in simple terms with analogies
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.market_data = MarketDataService()
        self.ta_engine = TechnicalAnalysisEngine()
        
        # Educational analogies for technical concepts
        self.analogies = {
            "bollinger_bands": "Think of Bollinger Bands as a rubber band around price - when it's squeezed tight, it means the stock is coiled like a spring ready to make a big move",
            "momentum": "The momentum is like a car accelerating uphill - when it's positive and building, it shows bullish strength",
            "histogram_building": "The momentum histogram shows bars getting taller like a ball bouncing higher each time after hitting bottom",
            "volume_confirmation": "We need enough volume - like needing enough buyers/sellers at a busy marketplace to make the move meaningful",
            "ema_alignment": "Moving averages are like layers of support - when they're stacked properly, they create a strong foundation"
        }
    
    async def analyze_ttm_squeeze_with_cot(self, symbol: str, timeframe: str = "5min") -> Optional[ChainOfThoughtAnalysis]:
        """
        Perform complete TTM Squeeze analysis with chain-of-thought explanations
        """
        try:
            # Get market data
            historical_data = await self.market_data.get_historical_data(symbol, timeframe, 50)
            quote = await self.market_data.get_real_time_quote(symbol)
            
            if len(historical_data) < 30:
                return None
            
            # Calculate technical indicators
            indicators = self.ta_engine.calculate_indicators(historical_data)
            
            # Build chain-of-thought analysis
            steps = []
            confidence_score = 0.0
            
            # Step 1: Technical Indicator Analysis
            step1, conf1 = self._analyze_technical_setup(symbol, indicators, historical_data)
            steps.append(step1)
            confidence_score += conf1
            
            # Step 2: Momentum Analysis
            step2, conf2 = self._analyze_momentum_conditions(symbol, indicators, historical_data)
            steps.append(step2)
            confidence_score += conf2
            
            # Step 3: Volume Confirmation
            step3, conf3 = self._analyze_volume_confirmation(symbol, indicators, quote)
            steps.append(step3)
            confidence_score += conf3
            
            # Step 4: Multi-timeframe Synthesis
            step4, conf4 = await self._analyze_multi_timeframe(symbol, indicators)
            steps.append(step4)
            confidence_score += conf4
            
            # Step 5: Risk Assessment
            step5, conf5 = self._analyze_risk_factors(symbol, indicators, quote)
            steps.append(step5)
            confidence_score += conf5
            
            # Step 6: Final Recommendation
            final_confidence = min(confidence_score / 5.0, 1.0)  # Average and cap at 100%
            step6 = self._generate_final_recommendation(symbol, final_confidence, indicators)
            steps.append(step6)
            
            # Generate educational notes
            educational_notes = self._generate_educational_notes(indicators, final_confidence)
            
            return ChainOfThoughtAnalysis(
                symbol=symbol,
                analysis_type="ttm_squeeze_cot",
                steps=steps,
                final_confidence=final_confidence,
                final_recommendation=step6.explanation,
                risk_assessment=step5.explanation,
                educational_notes=educational_notes
            )
            
        except Exception as e:
            self.logger.error(f"Error in chain-of-thought analysis for {symbol}: {e}")
            return None
    
    def _analyze_technical_setup(self, symbol: str, indicators: TechnicalIndicators, 
                                historical_data: List[OHLCV]) -> Tuple[ChainOfThoughtStep, float]:
        """Step 1: Analyze technical indicator setup"""
        
        # Calculate Keltner Channels for TTM Squeeze
        kc_upper = indicators.sma_20 + (indicators.atr * 1.5) if indicators.sma_20 and indicators.atr else 0
        kc_lower = indicators.sma_20 - (indicators.atr * 1.5) if indicators.sma_20 and indicators.atr else 0
        
        # Check squeeze condition
        squeeze_active = False
        squeeze_ratio = 0.0
        
        if indicators.bb_upper and indicators.bb_lower and kc_upper and kc_lower:
            squeeze_active = indicators.bb_upper < kc_upper and indicators.bb_lower > kc_lower
            bb_width = indicators.bb_upper - indicators.bb_lower
            kc_width = kc_upper - kc_lower
            squeeze_ratio = bb_width / kc_width if kc_width > 0 else 0
        
        # Build explanation
        if squeeze_active:
            explanation = f"TTM Squeeze is ACTIVE! Bollinger Bands (${indicators.bb_upper:.2f} to ${indicators.bb_lower:.2f}) are squeezed inside Keltner Channels (${kc_upper:.2f} to ${kc_lower:.2f}). Compression ratio: {squeeze_ratio:.2f}"
            confidence = 0.8 if squeeze_ratio < 0.8 else 0.6  # Tighter squeeze = higher confidence
        else:
            explanation = f"No TTM Squeeze detected. Bollinger Bands are wider than Keltner Channels, indicating normal volatility."
            confidence = 0.2
        
        return ChainOfThoughtStep(
            step_number=1,
            category="technical",
            title="Technical Indicator Setup Analysis",
            explanation=explanation,
            analogy=self.analogies["bollinger_bands"],
            technical_values={
                "bb_upper": float(indicators.bb_upper or 0),
                "bb_lower": float(indicators.bb_lower or 0),
                "kc_upper": float(kc_upper),
                "kc_lower": float(kc_lower),
                "squeeze_ratio": squeeze_ratio,
                "squeeze_active": float(squeeze_active)
            },
            confidence_contribution=confidence
        ), confidence
    
    def _analyze_momentum_conditions(self, symbol: str, indicators: TechnicalIndicators,
                                   historical_data: List[OHLCV]) -> Tuple[ChainOfThoughtStep, float]:
        """Step 2: Analyze momentum and EMA conditions"""
        
        # Get recent price data for momentum analysis
        recent_closes = [bar.close for bar in historical_data[-10:]]
        current_price = recent_closes[-1]
        
        # EMA analysis
        ema5 = indicators.ema_12  # Using available EMA as proxy
        ema8_momentum = 0.0
        momentum_building = False
        
        if len(recent_closes) >= 5:
            # Calculate simple momentum
            price_momentum = current_price - recent_closes[-5]
            momentum_building = price_momentum > 0
            
            if ema5:
                ema8_momentum = ema5 - (sum(recent_closes[-5:-1]) / 4)  # Approximate EMA momentum
        
        # Build explanation
        if momentum_building and ema8_momentum > 0:
            explanation = f"BULLISH momentum detected! Current price (${current_price:.2f}) is above its 5-day average, and the trend is accelerating upward. This is like a car gaining speed uphill."
            confidence = 0.8
        elif momentum_building:
            explanation = f"Moderate bullish momentum. Price is rising but trend strength is mixed. Like a car maintaining steady speed."
            confidence = 0.6
        else:
            explanation = f"Weak or negative momentum. Price trend is not supporting a bullish breakout. Like a car slowing down."
            confidence = 0.3
        
        return ChainOfThoughtStep(
            step_number=2,
            category="momentum", 
            title="Momentum & EMA Filter Analysis",
            explanation=explanation,
            analogy=self.analogies["momentum"],
            technical_values={
                "current_price": current_price,
                "ema5": float(ema5 or 0),
                "price_momentum": float(recent_closes[-1] - recent_closes[-5] if len(recent_closes) >= 5 else 0),
                "ema8_momentum": float(ema8_momentum),
                "momentum_building": float(momentum_building)
            },
            confidence_contribution=confidence
        ), confidence
    
    def _analyze_volume_confirmation(self, symbol: str, indicators: TechnicalIndicators,
                                   quote) -> Tuple[ChainOfThoughtStep, float]:
        """Step 3: Analyze volume confirmation"""
        
        current_volume = quote.volume
        avg_volume = indicators.volume_sma or 1
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
        
        # Volume analysis
        if volume_ratio >= 2.0:
            explanation = f"EXCELLENT volume confirmation! Current volume ({current_volume:,}) is {volume_ratio:.1f}x the 20-day average ({avg_volume:,.0f}). This is like a packed marketplace with lots of buyers and sellers."
            confidence = 0.9
        elif volume_ratio >= 1.5:
            explanation = f"Good volume support. Current volume is {volume_ratio:.1f}x average, showing decent interest in the breakout."
            confidence = 0.7
        elif volume_ratio >= 1.0:
            explanation = f"Average volume. Volume is {volume_ratio:.1f}x normal - adequate but not exceptional for a strong breakout."
            confidence = 0.5
        else:
            explanation = f"LOW volume warning! Volume is only {volume_ratio:.1f}x average. This breakout might lack conviction - like a quiet marketplace."
            confidence = 0.2
        
        return ChainOfThoughtStep(
            step_number=3,
            category="volume",
            title="Volume Confirmation Analysis",
            explanation=explanation,
            analogy=self.analogies["volume_confirmation"],
            technical_values={
                "current_volume": float(current_volume),
                "average_volume": float(avg_volume),
                "volume_ratio": volume_ratio
            },
            confidence_contribution=confidence
        ), confidence

    async def _analyze_multi_timeframe(self, symbol: str, indicators: TechnicalIndicators) -> Tuple[ChainOfThoughtStep, float]:
        """Step 4: Multi-timeframe analysis"""

        try:
            # Get different timeframe data
            daily_data = await self.market_data.get_historical_data(symbol, "1Day", 20)
            hourly_data = await self.market_data.get_historical_data(symbol, "1Hour", 20)

            timeframe_alignment = 0
            total_timeframes = 3

            # Current timeframe (5min) - already analyzed
            timeframe_alignment += 1  # Assume current analysis is positive

            # Daily timeframe analysis
            if daily_data and len(daily_data) >= 10:
                daily_indicators = self.ta_engine.calculate_indicators(daily_data)
                if daily_indicators.rsi and 30 < daily_indicators.rsi < 70:  # Not overbought/oversold
                    timeframe_alignment += 1

            # Hourly timeframe analysis
            if hourly_data and len(hourly_data) >= 10:
                hourly_indicators = self.ta_engine.calculate_indicators(hourly_data)
                if hourly_indicators.macd and hourly_indicators.macd_signal:
                    if hourly_indicators.macd > hourly_indicators.macd_signal:  # Bullish MACD
                        timeframe_alignment += 1

            alignment_percentage = (timeframe_alignment / total_timeframes) * 100

            if alignment_percentage >= 80:
                explanation = f"STRONG multi-timeframe alignment! {timeframe_alignment}/{total_timeframes} timeframes agree ({alignment_percentage:.0f}%). Like having hourly, daily, and weekly weather forecasts all predicting sunshine."
                confidence = 0.9
            elif alignment_percentage >= 60:
                explanation = f"Good timeframe alignment. {timeframe_alignment}/{total_timeframes} timeframes support the signal ({alignment_percentage:.0f}%)."
                confidence = 0.7
            else:
                explanation = f"Mixed timeframe signals. Only {timeframe_alignment}/{total_timeframes} timeframes align ({alignment_percentage:.0f}%). Proceed with caution."
                confidence = 0.4

        except Exception as e:
            explanation = "Unable to analyze multiple timeframes due to data limitations. Using single timeframe analysis only."
            confidence = 0.5
            alignment_percentage = 50.0
            timeframe_alignment = 1

        return ChainOfThoughtStep(
            step_number=4,
            category="multi_timeframe",
            title="Multi-Timeframe Signal Synthesis",
            explanation=explanation,
            analogy="Like checking weather on hourly, daily, and weekly forecasts - all timeframes must agree for highest confidence trades",
            technical_values={
                "timeframes_aligned": float(timeframe_alignment),
                "total_timeframes": float(total_timeframes),
                "alignment_percentage": alignment_percentage
            },
            confidence_contribution=confidence
        ), confidence

    def _analyze_risk_factors(self, symbol: str, indicators: TechnicalIndicators, quote) -> Tuple[ChainOfThoughtStep, float]:
        """Step 5: Risk factor analysis"""

        risk_factors = []
        risk_score = 1.0  # Start with perfect score, deduct for risks

        # Volatility risk
        if indicators.atr and quote.price:
            atr_percentage = (indicators.atr / quote.price) * 100
            if atr_percentage > 5.0:
                risk_factors.append(f"High volatility: {atr_percentage:.1f}% ATR - stock moves wildly")
                risk_score -= 0.2
            elif atr_percentage > 3.0:
                risk_factors.append(f"Moderate volatility: {atr_percentage:.1f}% ATR")
                risk_score -= 0.1

        # Overbought/oversold conditions
        if indicators.rsi:
            if indicators.rsi > 80:
                risk_factors.append(f"Overbought warning: RSI at {indicators.rsi:.1f} - may pullback")
                risk_score -= 0.3
            elif indicators.rsi < 20:
                risk_factors.append(f"Oversold condition: RSI at {indicators.rsi:.1f}")
                risk_score -= 0.1

        # Market timing risk (simplified)
        current_hour = datetime.now().hour
        if current_hour < 10 or current_hour > 15:  # Outside main trading hours
            risk_factors.append("Trading outside main market hours - lower liquidity risk")
            risk_score -= 0.1

        # Build risk assessment
        if risk_score >= 0.8:
            risk_level = "LOW"
            explanation = f"Low risk setup. {len(risk_factors)} minor risk factors identified."
        elif risk_score >= 0.6:
            risk_level = "MODERATE"
            explanation = f"Moderate risk. {len(risk_factors)} risk factors to consider: {'; '.join(risk_factors[:2])}"
        else:
            risk_level = "HIGH"
            explanation = f"HIGH RISK setup! {len(risk_factors)} significant risk factors: {'; '.join(risk_factors)}"

        confidence = max(risk_score, 0.1)  # Risk score becomes confidence contribution

        return ChainOfThoughtStep(
            step_number=5,
            category="risk",
            title="Risk Factor Assessment",
            explanation=explanation,
            analogy="Like checking for storm clouds before going sailing - we need to know what could go wrong",
            technical_values={
                "risk_score": risk_score,
                "risk_level": risk_level,
                "risk_factor_count": float(len(risk_factors)),
                "atr_percentage": float((indicators.atr / quote.price) * 100 if indicators.atr and quote.price else 0),
                "rsi": float(indicators.rsi or 50)
            },
            confidence_contribution=confidence
        ), confidence

    def _generate_final_recommendation(self, symbol: str, final_confidence: float,
                                     indicators: TechnicalIndicators) -> ChainOfThoughtStep:
        """Step 6: Generate final recommendation"""

        confidence_percentage = final_confidence * 100

        if final_confidence >= 0.80:
            recommendation = f"STRONG BUY signal for {symbol}! Confidence: {confidence_percentage:.0f}% - This is like having 8+ out of 10 experts agree on the trade direction."
            action = "Consider taking a full position with tight stop-loss"
        elif final_confidence >= 0.65:
            recommendation = f"BUY signal for {symbol}. Confidence: {confidence_percentage:.0f}% - Good setup with most factors aligned."
            action = "Consider taking a partial position or wait for better entry"
        elif final_confidence >= 0.50:
            recommendation = f"WEAK BUY signal for {symbol}. Confidence: {confidence_percentage:.0f}% - Mixed signals, proceed with caution."
            action = "Small position only or wait for clearer setup"
        else:
            recommendation = f"NO TRADE recommended for {symbol}. Confidence: {confidence_percentage:.0f}% - Too many conflicting signals."
            action = "Wait for better opportunity"

        return ChainOfThoughtStep(
            step_number=6,
            category="final",
            title="Final Trading Recommendation",
            explanation=f"{recommendation} {action}",
            analogy=f"Signal strength: {confidence_percentage:.0f}% confidence - this is like having {confidence_percentage/10:.1f} out of 10 experts agree on the trade direction",
            technical_values={
                "final_confidence": final_confidence,
                "confidence_percentage": confidence_percentage,
                "recommended_action": action
            },
            confidence_contribution=final_confidence
        )

    def _generate_educational_notes(self, indicators: TechnicalIndicators, confidence: float) -> List[str]:
        """Generate educational notes for beginners"""

        notes = [
            "💡 TTM Squeeze occurs when Bollinger Bands squeeze inside Keltner Channels - like a coiled spring ready to release energy",
            "📊 Higher volume during breakouts indicates more conviction from traders - like more people voting for the same direction",
            "⚠️ Always use stop-losses to limit downside risk - never risk more than 2% of your account on any single trade",
            "🎯 This pattern works approximately 65% of the time historically - no strategy is 100% accurate",
            "📈 Multi-timeframe confirmation increases success probability - like getting multiple weather reports before planning outdoor activities"
        ]

        # Add confidence-specific notes
        if confidence >= 0.8:
            notes.append("✅ High confidence signals like this occur less frequently but have better success rates")
        elif confidence < 0.5:
            notes.append("⚠️ Low confidence signals should be avoided or traded with very small position sizes")

        # Add indicator-specific educational notes
        if indicators.rsi:
            if indicators.rsi > 70:
                notes.append("📉 RSI above 70 suggests overbought conditions - price may pullback before continuing higher")
            elif indicators.rsi < 30:
                notes.append("📈 RSI below 30 suggests oversold conditions - potential bounce opportunity")

        return notes
