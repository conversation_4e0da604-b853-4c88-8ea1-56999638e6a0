#!/usr/bin/env python3
"""
Streamlined A.T.L.A.S Testing Suite
Test all 7 core features and verify performance requirements
"""

import asyncio
import aiohttp
import time
import json
from datetime import datetime
from typing import Dict, List, Any


class StreamlinedAtlasTestSuite:
    """Comprehensive test suite for streamlined A.T.L.A.S system"""
    
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.test_results = []
        self.performance_metrics = {}
    
    def log_test(self, feature: str, success: bool, details: str, duration: float = 0):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            "feature": feature,
            "status": status,
            "success": success,
            "details": details,
            "duration": duration,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        perf_note = f" ({duration:.2f}s)" if duration > 0 else ""
        print(f"{status} {feature}: {details}{perf_note}")
    
    async def test_system_health(self):
        """Test system health and availability"""
        print("🏥 Testing System Health...")
        
        try:
            start_time = time.time()
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/") as response:
                    duration = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        self.log_test(
                            "System Health",
                            True,
                            f"Server running v{data.get('version', 'unknown')}",
                            duration
                        )
                        return True
                    else:
                        self.log_test("System Health", False, f"HTTP {response.status}")
                        return False
        except Exception as e:
            self.log_test("System Health", False, f"Connection failed: {e}")
            return False
    
    async def test_live_quotes_charting(self):
        """Test Core Feature 1: Live Quotes & Charting"""
        print("\n📈 Testing Live Quotes & Charting...")
        
        test_symbols = ["AAPL", "MSFT", "GOOGL"]
        
        async with aiohttp.ClientSession() as session:
            for symbol in test_symbols:
                try:
                    # Test real-time quote
                    start_time = time.time()
                    async with session.get(f"{self.base_url}/api/v1/quote/{symbol}") as response:
                        quote_duration = time.time() - start_time
                        
                        if response.status == 200:
                            data = await response.json()
                            success = quote_duration < 2.0 and "price" in data
                            self.log_test(
                                f"Live Quote ({symbol})",
                                success,
                                f"Price: ${data.get('price', 'N/A')}, Latency: {quote_duration:.2f}s",
                                quote_duration
                            )
                        else:
                            self.log_test(f"Live Quote ({symbol})", False, f"HTTP {response.status}")
                    
                    # Test historical data for charting
                    start_time = time.time()
                    payload = {
                        "symbol": symbol,
                        "timeframe": "1Day",
                        "limit": 50
                    }
                    async with session.post(
                        f"{self.base_url}/api/v1/historical",
                        json=payload
                    ) as response:
                        chart_duration = time.time() - start_time
                        
                        if response.status == 200:
                            data = await response.json()
                            success = len(data) > 0 and "open" in data[0]
                            self.log_test(
                                f"Chart Data ({symbol})",
                                success,
                                f"Bars: {len(data)}, Duration: {chart_duration:.2f}s",
                                chart_duration
                            )
                        else:
                            self.log_test(f"Chart Data ({symbol})", False, f"HTTP {response.status}")
                
                except Exception as e:
                    self.log_test(f"Live Quotes ({symbol})", False, f"Error: {e}")
    
    async def test_technical_scanner(self):
        """Test Core Feature 2: Technical Analysis Scanner"""
        print("\n🔍 Testing Technical Analysis Scanner...")
        
        scan_types = ["oversold", "breakout", "ttm_squeeze", "macd_bullish"]
        
        async with aiohttp.ClientSession() as session:
            for scan_type in scan_types:
                try:
                    start_time = time.time()
                    payload = {
                        "scan_type": scan_type,
                        "limit": 10
                    }
                    async with session.post(
                        f"{self.base_url}/api/v1/scan",
                        json=payload
                    ) as response:
                        duration = time.time() - start_time
                        
                        if response.status == 200:
                            data = await response.json()
                            success = duration < 5.0 and len(data) >= 0
                            self.log_test(
                                f"Scanner ({scan_type})",
                                success,
                                f"Results: {len(data)}, Duration: {duration:.2f}s",
                                duration
                            )
                        else:
                            self.log_test(f"Scanner ({scan_type})", False, f"HTTP {response.status}")
                
                except Exception as e:
                    self.log_test(f"Scanner ({scan_type})", False, f"Error: {e}")
            
            # Test comprehensive scan
            try:
                start_time = time.time()
                payload = {"scan_type": "comprehensive"}
                async with session.post(f"{self.base_url}/api/v1/scan", json=payload) as response:
                    duration = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        success = duration < 10.0 and isinstance(data, dict)
                        self.log_test(
                            "Comprehensive Scanner",
                            success,
                            f"Scan types: {len(data)}, Duration: {duration:.2f}s",
                            duration
                        )
                    else:
                        self.log_test("Comprehensive Scanner", False, f"HTTP {response.status}")
            
            except Exception as e:
                self.log_test("Comprehensive Scanner", False, f"Error: {e}")
    
    async def test_llm_qa_integration(self):
        """Test Core Feature 3: LLM Q&A Integration"""
        print("\n💬 Testing LLM Q&A Integration...")
        
        test_queries = [
            "Analyze AAPL and explain the current trend",
            "What is RSI and how do I use it?",
            "Explain support and resistance levels",
            "Why did TSLA move today?"
        ]
        
        async with aiohttp.ClientSession() as session:
            for query in test_queries:
                try:
                    start_time = time.time()
                    payload = {
                        "message": query,
                        "context": {"symbol": "AAPL"}
                    }
                    async with session.post(
                        f"{self.base_url}/api/v1/chat",
                        json=payload
                    ) as response:
                        duration = time.time() - start_time
                        
                        if response.status == 200:
                            data = await response.json()
                            success = duration < 10.0 and "response" in data and len(data["response"]) > 50
                            self.log_test(
                                f"LLM Query",
                                success,
                                f"Response length: {len(data.get('response', ''))}, Duration: {duration:.2f}s",
                                duration
                            )
                        else:
                            self.log_test("LLM Query", False, f"HTTP {response.status}")
                
                except Exception as e:
                    self.log_test("LLM Query", False, f"Error: {e}")
    
    async def test_portfolio_tracking(self):
        """Test Core Feature 5: Portfolio Tracking"""
        print("\n💼 Testing Portfolio Tracking...")
        
        async with aiohttp.ClientSession() as session:
            try:
                # Test portfolio metrics
                start_time = time.time()
                async with session.get(f"{self.base_url}/api/v1/portfolio") as response:
                    duration = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        success = "portfolio" in data and duration < 5.0
                        self.log_test(
                            "Portfolio Metrics",
                            success,
                            f"Account value: ${data.get('portfolio', {}).get('total_value', 'N/A')}, Duration: {duration:.2f}s",
                            duration
                        )
                    else:
                        self.log_test("Portfolio Metrics", False, f"HTTP {response.status}")
                
                # Test positions
                start_time = time.time()
                async with session.get(f"{self.base_url}/api/v1/positions") as response:
                    duration = time.time() - start_time
                    
                    if response.status == 200:
                        data = await response.json()
                        success = isinstance(data, list) and duration < 5.0
                        self.log_test(
                            "Position Tracking",
                            success,
                            f"Positions: {len(data)}, Duration: {duration:.2f}s",
                            duration
                        )
                    else:
                        self.log_test("Position Tracking", False, f"HTTP {response.status}")
            
            except Exception as e:
                self.log_test("Portfolio Tracking", False, f"Error: {e}")
    
    async def test_event_explanation(self):
        """Test Core Feature 6: Event Explanation Engine"""
        print("\n📰 Testing Event Explanation Engine...")
        
        test_symbols = ["AAPL", "TSLA"]
        
        async with aiohttp.ClientSession() as session:
            for symbol in test_symbols:
                try:
                    start_time = time.time()
                    async with session.get(f"{self.base_url}/api/v1/explain/{symbol}") as response:
                        duration = time.time() - start_time
                        
                        if response.status == 200:
                            data = await response.json()
                            success = duration < 10.0 and "explanation" in data
                            self.log_test(
                                f"Price Movement Explanation ({symbol})",
                                success,
                                f"Explanation provided, Duration: {duration:.2f}s",
                                duration
                            )
                        else:
                            self.log_test(f"Event Explanation ({symbol})", False, f"HTTP {response.status}")
                
                except Exception as e:
                    self.log_test(f"Event Explanation ({symbol})", False, f"Error: {e}")
    
    async def test_rag_education(self):
        """Test Core Feature 7: Teaching Mode (RAG)"""
        print("\n📚 Testing RAG Education System...")
        
        test_queries = [
            {
                "question": "What does Trading in the Zone say about psychology?",
                "book_filter": "Trading in the Zone"
            },
            {
                "question": "Explain the CAN SLIM system",
                "book_filter": "How to Make Money in Stocks"
            },
            {
                "question": "What is RSI?",
                "book_filter": None
            }
        ]
        
        async with aiohttp.ClientSession() as session:
            # Test available books
            try:
                async with session.get(f"{self.base_url}/api/v1/education/books") as response:
                    if response.status == 200:
                        data = await response.json()
                        self.log_test(
                            "RAG Books Available",
                            True,
                            f"Books: {len(data.get('books', []))}"
                        )
                    else:
                        self.log_test("RAG Books", False, f"HTTP {response.status}")
            except Exception as e:
                self.log_test("RAG Books", False, f"Error: {e}")
            
            # Test educational queries
            for query_data in test_queries:
                try:
                    start_time = time.time()
                    async with session.post(
                        f"{self.base_url}/api/v1/education/query",
                        json=query_data
                    ) as response:
                        duration = time.time() - start_time
                        
                        if response.status == 200:
                            data = await response.json()
                            success = duration < 5.0 and "answer" in data and len(data["answer"]) > 50
                            self.log_test(
                                "RAG Education Query",
                                success,
                                f"Answer length: {len(data.get('answer', ''))}, Duration: {duration:.2f}s",
                                duration
                            )
                        else:
                            self.log_test("RAG Education Query", False, f"HTTP {response.status}")
                
                except Exception as e:
                    self.log_test("RAG Education Query", False, f"Error: {e}")
    
    async def test_ai_stop_loss(self):
        """Test AI-enhanced stop loss calculation"""
        print("\n🛡️ Testing AI-Enhanced Stop Loss...")

        async with aiohttp.ClientSession() as session:
            try:
                start_time = time.time()
                params = {
                    "symbol": "AAPL",
                    "entry_price": 150.0,
                    "direction": "long",
                    "risk_percent": 2.0
                }
                async with session.post(
                    f"{self.base_url}/api/v1/ai/stop-loss",
                    params=params
                ) as response:
                    duration = time.time() - start_time

                    if response.status == 200:
                        data = await response.json()
                        success = "stop_price" in data and duration < 5.0
                        self.log_test(
                            "AI Stop Loss",
                            success,
                            f"Stop: ${data.get('stop_price', 'N/A')}, Method: {data.get('method', 'N/A')}, Duration: {duration:.2f}s",
                            duration
                        )
                    else:
                        self.log_test("AI Stop Loss", False, f"HTTP {response.status}")

            except Exception as e:
                self.log_test("AI Stop Loss", False, f"Error: {e}")

    async def test_enhanced_features(self):
        """Test enhanced personalization and validation features"""
        print("\n🚀 Testing Enhanced Features...")

        async with aiohttp.ClientSession() as session:
            # Test feedback collection
            try:
                feedback_payload = {
                    "message_id": "test_msg_123",
                    "feedback_type": "thumbs_up",
                    "rating": 5,
                    "comment": "Great analysis!"
                }
                async with session.post(
                    f"{self.base_url}/api/v1/feedback",
                    json=feedback_payload
                ) as response:
                    success = response.status == 200
                    self.log_test(
                        "Feedback Collection",
                        success,
                        f"Status: {response.status}"
                    )
            except Exception as e:
                self.log_test("Feedback Collection", False, f"Error: {e}")

            # Test preferences update
            try:
                preferences_payload = {
                    "max_risk_per_trade": 1.5,
                    "preferred_strategies": ["ttm_squeeze", "ema_crossover"],
                    "min_volume_ratio": 2.0
                }
                async with session.post(
                    f"{self.base_url}/api/v1/preferences",
                    json=preferences_payload
                ) as response:
                    success = response.status == 200
                    self.log_test(
                        "Preferences Update",
                        success,
                        f"Status: {response.status}"
                    )
            except Exception as e:
                self.log_test("Preferences Update", False, f"Error: {e}")

            # Test validation summary
            try:
                async with session.get(f"{self.base_url}/api/v1/validation/summary") as response:
                    success = response.status == 200
                    if success:
                        data = await response.json()
                        self.log_test(
                            "Validation Summary",
                            success,
                            f"Validations tracked: {data.get('total_validations', 0)}"
                        )
                    else:
                        self.log_test("Validation Summary", False, f"HTTP {response.status}")
            except Exception as e:
                self.log_test("Validation Summary", False, f"Error: {e}")

            # Test trading rules performance
            try:
                async with session.get(f"{self.base_url}/api/v1/trading-rules/performance") as response:
                    success = response.status == 200
                    if success:
                        data = await response.json()
                        performance = data.get("strategy_performance", {})
                        self.log_test(
                            "Trading Rules Performance",
                            success,
                            f"Strategies tracked: {len(performance)}"
                        )
                    else:
                        self.log_test("Trading Rules Performance", False, f"HTTP {response.status}")
            except Exception as e:
                self.log_test("Trading Rules Performance", False, f"Error: {e}")

    async def test_enhanced_chat_validation(self):
        """Test enhanced chat with validation and feedback"""
        print("\n💬 Testing Enhanced Chat with Validation...")

        test_queries = [
            "Analyze AAPL with TTM Squeeze strategy",
            "What's the best entry for TSLA breakout?",
            "Explain RSI divergence trading",
            "Should I buy NVDA at current levels?"
        ]

        async with aiohttp.ClientSession() as session:
            for query in test_queries:
                try:
                    start_time = time.time()
                    payload = {
                        "message": query,
                        "context": {"symbol": "AAPL"}
                    }
                    async with session.post(
                        f"{self.base_url}/api/v1/chat",
                        json=payload
                    ) as response:
                        duration = time.time() - start_time

                        if response.status == 200:
                            data = await response.json()
                            has_validation = "validation_passed" in data
                            has_response = "response" in data and len(data["response"]) > 50
                            success = duration < 10.0 and has_response

                            validation_status = "✅" if data.get("validation_passed") else "⚠️"
                            self.log_test(
                                f"Enhanced Chat ({validation_status})",
                                success,
                                f"Query: '{query[:30]}...', Validated: {has_validation}, Duration: {duration:.2f}s",
                                duration
                            )
                        else:
                            self.log_test("Enhanced Chat", False, f"HTTP {response.status}")

                except Exception as e:
                    self.log_test("Enhanced Chat", False, f"Error: {e}")

    async def test_enhanced_rag_system(self):
        """Test enhanced RAG system with new trading content"""
        print("\n📚 Testing Enhanced RAG System...")

        enhanced_queries = [
            "How do I set up a bull put spread?",
            "What are the phases of TTM Squeeze?",
            "Explain opening range breakout strategy",
            "How do I manage iron condor positions?",
            "What is Wyckoff accumulation pattern?",
            "How do I calculate option Greeks?",
            "What are fair value gaps in trading?",
            "Explain credit spread risk management"
        ]

        async with aiohttp.ClientSession() as session:
            for query in enhanced_queries:
                try:
                    start_time = time.time()
                    payload = {
                        "query": query,
                        "book_filter": None
                    }
                    async with session.post(
                        f"{self.base_url}/api/v1/education/query",
                        json=payload
                    ) as response:
                        duration = time.time() - start_time

                        if response.status == 200:
                            data = await response.json()
                            has_answer = "answer" in data and len(data["answer"]) > 100
                            has_sources = "sources" in data and len(data["sources"]) > 0
                            success = duration < 5.0 and has_answer

                            # Check for enhanced content indicators
                            answer = data.get("answer", "").lower()
                            enhanced_content = any(term in answer for term in [
                                "delta", "gamma", "theta", "vega", "iv rank",
                                "squeeze", "momentum", "breakout", "wyckoff",
                                "order blocks", "fair value", "credit spread"
                            ])

                            self.log_test(
                                f"Enhanced RAG ({query[:20]}...)",
                                success and enhanced_content,
                                f"Duration: {duration:.2f}s, Enhanced: {enhanced_content}, Sources: {len(data.get('sources', []))}"
                            )
                        else:
                            self.log_test("Enhanced RAG", False, f"HTTP {response.status}")

                except Exception as e:
                    self.log_test("Enhanced RAG", False, f"Error: {e}")
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*80)
        print("📊 STREAMLINED A.T.L.A.S TEST REPORT")
        print("="*80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"📈 Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   📊 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print(f"\n🎯 Core Features Status:")
        features = {
            "Live Quotes & Charting": ["Live Quote", "Chart Data"],
            "Technical Scanner": ["Scanner", "Comprehensive Scanner"],
            "LLM Q&A Integration": ["LLM Query"],
            "Portfolio Tracking": ["Portfolio Metrics", "Position Tracking"],
            "Event Explanation": ["Price Movement Explanation", "Event Explanation"],
            "RAG Education": ["RAG Books", "RAG Education Query"],
            "AI Stop Loss": ["AI Stop Loss"]
        }

        enhanced_features = {
            "Personalization & Feedback": ["Feedback Collection", "Preferences Update"],
            "Validation & Safety": ["Validation Summary", "Trading Rules Performance"],
            "Enhanced Chat": ["Enhanced Chat"],
            "Enhanced RAG System": ["Enhanced RAG"]
        }
        
        for feature, test_names in features.items():
            feature_tests = [r for r in self.test_results if any(name in r["feature"] for name in test_names)]
            feature_success = all(r["success"] for r in feature_tests)
            status = "✅" if feature_success else "❌"
            print(f"   {status} {feature}")

        print(f"\n🚀 Enhanced Features Status:")
        for feature, test_names in enhanced_features.items():
            feature_tests = [r for r in self.test_results if any(name in r["feature"] for name in test_names)]
            feature_success = all(r["success"] for r in feature_tests) if feature_tests else False
            status = "✅" if feature_success else "❌"
            print(f"   {status} {feature}")
        
        print(f"\n⚡ Performance Metrics:")
        quote_tests = [r for r in self.test_results if "Live Quote" in r["feature"] and r["success"]]
        if quote_tests:
            avg_quote_time = sum(r["duration"] for r in quote_tests) / len(quote_tests)
            print(f"   📈 Average Quote Latency: {avg_quote_time:.2f}s (Target: <2s)")
        
        scanner_tests = [r for r in self.test_results if "Scanner" in r["feature"] and r["success"]]
        if scanner_tests:
            avg_scanner_time = sum(r["duration"] for r in scanner_tests) / len(scanner_tests)
            print(f"   🔍 Average Scanner Time: {avg_scanner_time:.2f}s (Target: <5s)")
        
        llm_tests = [r for r in self.test_results if "LLM Query" in r["feature"] and r["success"]]
        if llm_tests:
            avg_llm_time = sum(r["duration"] for r in llm_tests) / len(llm_tests)
            print(f"   💬 Average LLM Response: {avg_llm_time:.2f}s (Target: <10s)")
        
        print(f"\n🏆 System Status: {'READY FOR PRODUCTION' if passed_tests/total_tests > 0.8 else 'NEEDS ATTENTION'}")
        print("="*80)
    
    async def run_all_tests(self):
        """Run comprehensive test suite"""
        print("🧪 Starting Streamlined A.T.L.A.S Test Suite")
        print("="*80)
        
        start_time = time.time()
        
        # Test system health first
        if not await self.test_system_health():
            print("❌ System health check failed. Aborting tests.")
            return
        
        # Run all feature tests
        await self.test_live_quotes_charting()
        await self.test_technical_scanner()
        await self.test_llm_qa_integration()
        await self.test_portfolio_tracking()
        await self.test_event_explanation()
        await self.test_rag_education()
        await self.test_ai_stop_loss()

        # Test enhanced features
        await self.test_enhanced_features()
        await self.test_enhanced_chat_validation()
        await self.test_enhanced_rag_system()
        
        total_duration = time.time() - start_time
        
        # Generate report
        self.generate_report()
        print(f"\n⏱️ Total Test Duration: {total_duration:.2f} seconds")


async def main():
    """Run the test suite"""
    test_suite = StreamlinedAtlasTestSuite()
    await test_suite.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
