"""
A.T.L.A.S Profit-Targeted Strategy Engine
Intelligent trade allocation system that works backwards from profit goals
"""

import asyncio
import logging
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal

from .config import settings, POPULAR_SYMBOLS
from .models import (
    ProfitTargetedStrategy, RiskManagementProfile, ChainOfThoughtAnalysis,
    TradingSignal, ScanResult, Quote
)
from .market_data import MarketDataService
from .chain_of_thought_engine import ChainOfThoughtEngine
from .technical_analysis import TechnicalScanner


class ProfitTargetedStrategyEngine:
    """
    Goal-oriented portfolio strategy engine that creates trading plans
    based on specific profit targets with comprehensive risk management
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.market_data = MarketDataService()
        self.cot_engine = ChainOfThoughtEngine()
        self.scanner = TechnicalScanner()
        
        # Historical performance data for TTM Squeeze
        self.ttm_squeeze_stats = {
            "win_rate": 0.65,  # 65% historical win rate
            "avg_win": 0.08,   # 8% average winning trade
            "avg_loss": 0.03,  # 3% average losing trade
            "max_drawdown": 0.15  # 15% maximum historical drawdown
        }
        
        # S&P 500 constituents for scanning (subset for performance)
        self.sp500_symbols = [
            "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "BRK.B",
            "UNH", "JNJ", "JPM", "V", "PG", "HD", "MA", "BAC", "ABBV", "PFE",
            "KO", "AVGO", "PEP", "TMO", "COST", "WMT", "DIS", "ABT", "DHR",
            "VZ", "ADBE", "CRM", "NFLX", "XOM", "NKE", "CMCSA", "T", "INTC"
        ]
    
    async def create_profit_targeted_strategy(self, profit_target: float, 
                                            account_size: float,
                                            timeframe: str = "intraday",
                                            risk_tolerance: str = "moderate") -> Optional[ProfitTargetedStrategy]:
        """
        Create a comprehensive trading strategy based on profit target
        """
        try:
            # Validate inputs and set realistic expectations
            validated_target = self._validate_profit_target(profit_target, account_size, timeframe)
            
            # Create risk management profile
            risk_profile = self._create_risk_profile(account_size, risk_tolerance)
            
            # Calculate position sizing using Kelly Criterion
            kelly_fraction = self._calculate_kelly_fraction()
            
            # Determine optimal number of positions for diversification
            target_positions = self._calculate_target_positions(validated_target, account_size, risk_profile)
            
            # Scan for high-probability setups
            candidate_symbols = await self._scan_for_opportunities()
            
            # Generate strategy reasoning
            strategy_reasoning = self._generate_strategy_reasoning(
                validated_target, account_size, timeframe, len(candidate_symbols)
            )
            
            strategy = ProfitTargetedStrategy(
                strategy_id=f"atlas_strategy_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                profit_target=validated_target,
                timeframe=timeframe,
                max_risk_per_trade=validated_target * 0.1,  # Risk 10% of target per trade
                max_risk_percent=risk_profile.daily_loss_limit_percent,
                target_positions=target_positions,
                sector_diversification=True,
                symbols_to_scan=candidate_symbols[:20],  # Top 20 candidates
                strategy_reasoning=strategy_reasoning,
                kelly_fraction=kelly_fraction,
                expected_win_rate=self.ttm_squeeze_stats["win_rate"]
            )
            
            return strategy
            
        except Exception as e:
            self.logger.error(f"Error creating profit-targeted strategy: {e}")
            return None
    
    def _validate_profit_target(self, target: float, account_size: float, timeframe: str) -> float:
        """Validate and adjust profit target based on realistic expectations"""
        
        target_percentage = (target / account_size) * 100
        
        # Set realistic limits based on timeframe
        if timeframe == "intraday":
            max_daily_target = 3.0  # 3% max daily target
            if target_percentage > max_daily_target:
                adjusted_target = account_size * (max_daily_target / 100)
                self.logger.warning(f"Profit target adjusted from ${target:.2f} to ${adjusted_target:.2f} ({max_daily_target}% of account)")
                return adjusted_target
        elif timeframe == "swing":
            max_weekly_target = 8.0  # 8% max weekly target
            if target_percentage > max_weekly_target:
                adjusted_target = account_size * (max_weekly_target / 100)
                return adjusted_target
        
        # Minimum target validation
        min_target = account_size * 0.005  # 0.5% minimum
        if target < min_target:
            return min_target
        
        return target
    
    def _create_risk_profile(self, account_size: float, risk_tolerance: str) -> RiskManagementProfile:
        """Create risk management profile based on account size and tolerance"""
        
        if risk_tolerance == "conservative":
            daily_loss_limit = 1.5
            max_correlation = 0.7
            volatility_threshold = 30.0
            min_confidence = 0.80
        elif risk_tolerance == "aggressive":
            daily_loss_limit = 4.0
            max_correlation = 0.9
            volatility_threshold = 50.0
            min_confidence = 0.60
        else:  # moderate
            daily_loss_limit = 2.5
            max_correlation = 0.85
            volatility_threshold = 40.0
            min_confidence = 0.70
        
        return RiskManagementProfile(
            account_size=account_size,
            daily_loss_limit_percent=daily_loss_limit,
            max_correlation=max_correlation,
            volatility_threshold=volatility_threshold,
            min_confidence_threshold=min_confidence,
            paper_trading_required=account_size < 25000,  # PDT rule
            position_sizing_method="kelly_criterion"
        )
    
    def _calculate_kelly_fraction(self) -> float:
        """Calculate Kelly Criterion fraction for optimal position sizing"""
        
        win_rate = self.ttm_squeeze_stats["win_rate"]
        avg_win = self.ttm_squeeze_stats["avg_win"]
        avg_loss = self.ttm_squeeze_stats["avg_loss"]
        
        # Kelly formula: f = (bp - q) / b
        # where b = odds received (avg_win/avg_loss), p = win probability, q = loss probability
        b = avg_win / avg_loss if avg_loss > 0 else 2.0
        p = win_rate
        q = 1 - win_rate
        
        kelly_fraction = (b * p - q) / b
        
        # Cap Kelly fraction at 25% for safety (fractional Kelly)
        return min(kelly_fraction * 0.5, 0.25)
    
    def _calculate_target_positions(self, profit_target: float, account_size: float, 
                                  risk_profile: RiskManagementProfile) -> int:
        """Calculate optimal number of positions for diversification"""
        
        # Base calculation on risk per trade and total risk budget
        max_risk_per_trade = account_size * (risk_profile.daily_loss_limit_percent / 100) * 0.4  # 40% of daily limit per trade
        
        # Estimate positions needed based on expected return per trade
        expected_return_per_trade = profit_target * 0.3  # Assume each trade contributes 30% of target
        estimated_positions = max(2, min(6, int(profit_target / expected_return_per_trade)))
        
        # Adjust for account size
        if account_size < 10000:
            estimated_positions = min(estimated_positions, 3)  # Limit positions for small accounts
        elif account_size > 100000:
            estimated_positions = min(estimated_positions, 8)  # Allow more positions for large accounts
        
        return estimated_positions
    
    async def _scan_for_opportunities(self) -> List[str]:
        """Scan S&P 500 for high-probability TTM Squeeze setups"""
        
        try:
            # Use existing scanner for TTM Squeeze
            squeeze_results = await self.scanner.scan_ttm_squeeze(self.sp500_symbols)
            
            # Filter for high-quality setups
            high_quality_symbols = []
            
            for result in squeeze_results:
                if result.score >= 70:  # High squeeze intensity
                    # Additional quality filters
                    volume_ratio = result.indicators.get("volume_ratio", 1.0)
                    if volume_ratio >= 1.5:  # Good volume
                        high_quality_symbols.append(result.symbol)
            
            # If not enough high-quality setups, include moderate ones
            if len(high_quality_symbols) < 5:
                for result in squeeze_results:
                    if result.score >= 50 and result.symbol not in high_quality_symbols:
                        high_quality_symbols.append(result.symbol)
                        if len(high_quality_symbols) >= 10:
                            break
            
            return high_quality_symbols
            
        except Exception as e:
            self.logger.error(f"Error scanning for opportunities: {e}")
            return self.sp500_symbols[:10]  # Fallback to top 10 symbols
    
    def _generate_strategy_reasoning(self, profit_target: float, account_size: float, 
                                   timeframe: str, num_candidates: int) -> str:
        """Generate human-readable strategy reasoning"""
        
        target_percentage = (profit_target / account_size) * 100
        
        reasoning = f"""
🎯 PROFIT-TARGETED STRATEGY ANALYSIS

Target: ${profit_target:.2f} ({target_percentage:.1f}% of account)
Timeframe: {timeframe.title()}
Account Size: ${account_size:,.2f}

📊 STRATEGY FOUNDATION:
• Using TTM Squeeze pattern with {self.ttm_squeeze_stats['win_rate']*100:.0f}% historical win rate
• Kelly Criterion position sizing for optimal risk-adjusted returns
• Scanning {len(self.sp500_symbols)} S&P 500 stocks for highest probability setups
• Found {num_candidates} qualifying candidates meeting our criteria

🛡️ RISK MANAGEMENT:
• Maximum 2.5% daily account risk (${account_size * 0.025:.2f})
• Position correlation limit: 85% (avoid concentrated sector risk)
• Automatic stop-losses on every trade
• Paper trading mode for accounts under $25,000

💡 EDUCATIONAL INSIGHT:
This strategy works backwards from your profit goal, using mathematical position sizing
and proven technical patterns. Think of it like planning a road trip - we know the
destination (profit target) and use the best route (TTM Squeeze) with safety measures
(risk management) to get there reliably.

⚠️ IMPORTANT: No strategy is guaranteed. This approach aims to maximize probability
of success while limiting downside risk. Always trade with money you can afford to lose.
        """.strip()
        
        return reasoning
    
    async def execute_strategy_scan(self, strategy: ProfitTargetedStrategy) -> List[Dict[str, Any]]:
        """Execute comprehensive scan for strategy implementation"""
        
        trade_opportunities = []
        
        try:
            # Analyze each candidate symbol with chain-of-thought
            for symbol in strategy.symbols_to_scan[:10]:  # Limit to top 10 for performance
                
                # Get chain-of-thought analysis
                cot_analysis = await self.cot_engine.analyze_ttm_squeeze_with_cot(symbol)
                
                if cot_analysis and cot_analysis.final_confidence >= 0.65:
                    
                    # Calculate position size using Kelly Criterion
                    position_size = self._calculate_position_size(
                        strategy, cot_analysis.final_confidence
                    )
                    
                    # Get current quote for entry price
                    quote = await self.market_data.get_real_time_quote(symbol)
                    
                    opportunity = {
                        "symbol": symbol,
                        "confidence": cot_analysis.final_confidence,
                        "entry_price": quote.price,
                        "position_size": position_size,
                        "risk_amount": position_size * quote.price * 0.03,  # 3% stop loss
                        "target_profit": position_size * quote.price * 0.08,  # 8% target
                        "reasoning": cot_analysis.final_recommendation,
                        "educational_notes": cot_analysis.educational_notes[:3],  # Top 3 notes
                        "chain_of_thought": [step.explanation for step in cot_analysis.steps]
                    }
                    
                    trade_opportunities.append(opportunity)
            
            # Sort by confidence and return top opportunities
            trade_opportunities.sort(key=lambda x: x["confidence"], reverse=True)
            return trade_opportunities[:strategy.target_positions]
            
        except Exception as e:
            self.logger.error(f"Error executing strategy scan: {e}")
            return []
    
    def _calculate_position_size(self, strategy: ProfitTargetedStrategy, confidence: float) -> int:
        """Calculate position size using Kelly Criterion and confidence adjustment"""
        
        # Base Kelly fraction adjusted by confidence
        adjusted_kelly = strategy.kelly_fraction * confidence
        
        # Calculate dollar amount to risk
        risk_amount = strategy.max_risk_per_trade * adjusted_kelly
        
        # Assume 3% stop loss for position sizing
        stop_loss_percent = 0.03
        position_value = risk_amount / stop_loss_percent
        
        # Convert to shares (simplified - would need current price)
        # This is a placeholder - actual implementation would use current price
        estimated_shares = int(position_value / 100)  # Assume $100 average price
        
        return max(1, estimated_shares)  # Minimum 1 share
