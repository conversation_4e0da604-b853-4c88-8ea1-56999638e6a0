"""
A.T.L.A.S Chain-of-Thought Trading Orchestrator
Master orchestrator that integrates all Chain-of-Thought components
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any

from .config import settings
from .models import (
    ChainOfThoughtAnalysis, ProfitTargetedStrategy, RiskManagementProfile,
    OptionsEducationalAnalysis, Quote, Position
)
from .chain_of_thought_engine import ChainOfThoughtEngine
from .profit_strategy_engine import ProfitTargetedStrategyEngine
from .risk_management_engine import RiskManagementEngine, PositionSizeCalculation, PreTradeValidation
from .options_education_engine import OptionsEducationEngine
from .execution_monitoring_engine import ExecutionMonitoringEngine, LiveTradeExecution, PortfolioMonitoring
from .market_data import MarketDataService


class ChainOfThoughtTradingOrchestrator:
    """
    Master orchestrator for Chain-of-Thought trading system
    Provides comprehensive beginner-friendly trading intelligence
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Initialize all engines
        self.cot_engine = ChainOfThoughtEngine()
        self.strategy_engine = ProfitTargetedStrategyEngine()
        self.risk_engine = RiskManagementEngine()
        self.options_engine = OptionsEducationEngine()
        self.execution_engine = ExecutionMonitoringEngine()
        self.market_data = MarketDataService()
        
        # Safety guardrails
        self.safety_guardrails = {
            "daily_loss_limit_percent": 3.0,
            "max_position_correlation": 0.85,
            "vix_trading_threshold": 40.0,
            "min_confidence_threshold": 0.70,
            "paper_trading_required": True,
            "max_positions": 6
        }
    
    async def create_comprehensive_trading_plan(self, 
                                              user_request: str,
                                              account_size: float,
                                              risk_tolerance: str = "moderate") -> Dict[str, Any]:
        """
        Create comprehensive trading plan with Chain-of-Thought analysis
        """
        
        try:
            # Parse user request for profit target
            profit_target = self._extract_profit_target(user_request, account_size)
            
            # Create profit-targeted strategy
            strategy = await self.strategy_engine.create_profit_targeted_strategy(
                profit_target=profit_target,
                account_size=account_size,
                timeframe="intraday",
                risk_tolerance=risk_tolerance
            )
            
            if not strategy:
                return {
                    "success": False,
                    "message": "Unable to create strategy with current parameters",
                    "educational_note": "Try adjusting your profit target or risk tolerance"
                }
            
            # Execute strategy scan with Chain-of-Thought analysis
            trade_opportunities = await self.strategy_engine.execute_strategy_scan(strategy)
            
            # Analyze each opportunity with full Chain-of-Thought
            detailed_analyses = []
            for opportunity in trade_opportunities[:3]:  # Top 3 opportunities
                
                # Get comprehensive Chain-of-Thought analysis
                cot_analysis = await self.cot_engine.analyze_ttm_squeeze_with_cot(
                    opportunity["symbol"]
                )
                
                if cot_analysis:
                    # Calculate position sizing
                    position_calc = self.risk_engine.calculate_position_size(
                        symbol=opportunity["symbol"],
                        entry_price=opportunity["entry_price"],
                        account_size=account_size,
                        confidence=cot_analysis.final_confidence,
                        risk_profile=RiskManagementProfile(
                            account_size=account_size,
                            daily_loss_limit_percent=self.safety_guardrails["daily_loss_limit_percent"]
                        )
                    )
                    
                    # Pre-trade validation
                    validation = self.risk_engine.validate_trade_setup(
                        symbol=opportunity["symbol"],
                        entry_price=opportunity["entry_price"],
                        position_size=position_calc,
                        current_positions=[],  # Would get actual positions
                        cot_analysis=cot_analysis
                    )
                    
                    detailed_analyses.append({
                        "symbol": opportunity["symbol"],
                        "chain_of_thought": cot_analysis,
                        "position_sizing": position_calc,
                        "validation": validation,
                        "opportunity_data": opportunity
                    })
            
            # Generate comprehensive plan
            comprehensive_plan = self._generate_comprehensive_plan(
                strategy, detailed_analyses, account_size
            )
            
            return {
                "success": True,
                "strategy": strategy,
                "trade_opportunities": detailed_analyses,
                "comprehensive_plan": comprehensive_plan,
                "safety_notes": self._generate_safety_notes(),
                "educational_summary": self._generate_educational_summary(strategy, detailed_analyses)
            }
            
        except Exception as e:
            self.logger.error(f"Error creating comprehensive trading plan: {e}")
            return {
                "success": False,
                "message": f"Error creating plan: {str(e)}",
                "educational_note": "Please try again or contact support if the issue persists"
            }
    
    async def execute_trade_with_full_cot(self, 
                                        symbol: str,
                                        account_size: float,
                                        confidence_override: Optional[float] = None) -> Dict[str, Any]:
        """
        Execute trade with full Chain-of-Thought analysis and protection
        """
        
        try:
            # Step 1: Chain-of-Thought Analysis
            cot_analysis = await self.cot_engine.analyze_ttm_squeeze_with_cot(symbol)
            
            if not cot_analysis:
                return {
                    "success": False,
                    "message": f"Unable to analyze {symbol} - insufficient data",
                    "educational_note": "Try a different symbol or check market data availability"
                }
            
            # Step 2: Position Sizing with Risk Management
            quote = await self.market_data.get_real_time_quote(symbol)
            
            position_calc = self.risk_engine.calculate_position_size(
                symbol=symbol,
                entry_price=quote.price,
                account_size=account_size,
                confidence=confidence_override or cot_analysis.final_confidence,
                risk_profile=RiskManagementProfile(account_size=account_size)
            )
            
            # Step 3: Pre-trade Validation
            validation = self.risk_engine.validate_trade_setup(
                symbol=symbol,
                entry_price=quote.price,
                position_size=position_calc,
                current_positions=[],  # Would get actual positions
                cot_analysis=cot_analysis
            )
            
            # Step 4: Execute if validated
            if validation.is_valid:
                execution_result = await self.execution_engine.execute_trade_with_protection(
                    symbol=symbol,
                    position_calc=position_calc,
                    validation=validation
                )
                
                return {
                    "success": True,
                    "chain_of_thought": cot_analysis,
                    "position_sizing": position_calc,
                    "validation": validation,
                    "execution": execution_result,
                    "educational_summary": self._generate_execution_educational_summary(
                        cot_analysis, position_calc, execution_result
                    )
                }
            else:
                return {
                    "success": False,
                    "chain_of_thought": cot_analysis,
                    "position_sizing": position_calc,
                    "validation": validation,
                    "message": "Trade blocked by risk management",
                    "educational_note": "Safety first! The system prevented a potentially risky trade."
                }
                
        except Exception as e:
            self.logger.error(f"Error executing trade with CoT for {symbol}: {e}")
            return {
                "success": False,
                "message": f"Execution error: {str(e)}",
                "educational_note": "Technical error occurred - please try again"
            }
    
    async def get_portfolio_dashboard(self, account_size: float) -> Dict[str, Any]:
        """
        Get comprehensive portfolio dashboard with educational insights
        """
        
        try:
            # Real-time portfolio monitoring
            portfolio_monitoring = await self.execution_engine.monitor_portfolio_realtime(account_size)
            
            # Market condition assessment
            market_conditions = {
                "condition": portfolio_monitoring.market_condition.value,
                "trading_allowed": portfolio_monitoring.market_condition.value in ["normal", "volatile"],
                "vix_level": 25.0,  # Would get real VIX
                "market_hours": self._check_market_hours()
            }
            
            # Educational insights based on performance
            educational_insights = self._generate_portfolio_educational_insights(portfolio_monitoring)
            
            # Risk utilization analysis
            risk_analysis = {
                "daily_risk_used": portfolio_monitoring.daily_risk_used,
                "risk_limit_remaining": portfolio_monitoring.risk_limit_remaining,
                "risk_utilization_percent": (portfolio_monitoring.daily_risk_used / (account_size * 0.03)) * 100,
                "position_count": portfolio_monitoring.open_positions,
                "diversification_score": self._calculate_diversification_score()
            }
            
            return {
                "portfolio_monitoring": portfolio_monitoring,
                "market_conditions": market_conditions,
                "risk_analysis": risk_analysis,
                "educational_insights": educational_insights,
                "safety_status": self._check_safety_status(portfolio_monitoring),
                "next_actions": self._suggest_next_actions(portfolio_monitoring, market_conditions)
            }
            
        except Exception as e:
            self.logger.error(f"Error generating portfolio dashboard: {e}")
            return {
                "error": str(e),
                "educational_note": "Dashboard temporarily unavailable - please refresh"
            }
    
    def _extract_profit_target(self, user_request: str, account_size: float) -> float:
        """Extract profit target from user request"""
        
        # Simple extraction logic (would be more sophisticated)
        import re
        
        # Look for dollar amounts
        dollar_matches = re.findall(r'\$(\d+(?:,\d{3})*(?:\.\d{2})?)', user_request)
        if dollar_matches:
            return float(dollar_matches[0].replace(',', ''))
        
        # Look for percentage
        percent_matches = re.findall(r'(\d+(?:\.\d+)?)%', user_request)
        if percent_matches:
            return account_size * (float(percent_matches[0]) / 100)
        
        # Default to 1% of account
        return account_size * 0.01
    
    def _generate_comprehensive_plan(self, strategy: ProfitTargetedStrategy,
                                   analyses: List[Dict], account_size: float) -> str:
        """Generate comprehensive trading plan explanation"""
        
        plan = f"""
🎯 A.T.L.A.S COMPREHENSIVE TRADING PLAN

💰 PROFIT TARGET: ${strategy.profit_target:.2f} ({(strategy.profit_target/account_size)*100:.1f}% of account)
⏰ TIMEFRAME: {strategy.timeframe.title()}
🎲 EXPECTED WIN RATE: {strategy.expected_win_rate*100:.0f}%

📊 STRATEGY OVERVIEW:
{strategy.strategy_reasoning}

🔍 TOP OPPORTUNITIES ANALYZED:
"""
        
        for i, analysis in enumerate(analyses[:3], 1):
            cot = analysis["chain_of_thought"]
            plan += f"""
{i}. {analysis["symbol"]} - Confidence: {cot.final_confidence*100:.0f}%
   💡 {cot.final_recommendation}
   💰 Position Size: {analysis["position_sizing"].recommended_shares} shares (${analysis["position_sizing"].dollar_amount:,.2f})
   🛡️ Risk: ${analysis["position_sizing"].risk_amount:.2f} | Target: ${analysis["position_sizing"].target_price:.2f}
"""
        
        plan += f"""
🛡️ RISK MANAGEMENT:
• Maximum daily risk: ${account_size * 0.03:.2f} (3% of account)
• Position sizing: Kelly Criterion with safety factor
• Automatic stop-losses on every trade
• Real-time monitoring and alerts

📚 EDUCATIONAL NOTES:
• This plan uses proven TTM Squeeze patterns with {strategy.expected_win_rate*100:.0f}% historical success rate
• Each trade is sized mathematically for optimal risk-adjusted returns
• Multiple safety layers protect your capital
• System provides step-by-step reasoning for every decision

⚠️ IMPORTANT: No strategy is guaranteed. Always trade with money you can afford to lose.
        """.strip()
        
        return plan
    
    def _generate_safety_notes(self) -> List[str]:
        """Generate safety notes for users"""
        
        return [
            "🛡️ All trades include automatic stop-losses to limit downside risk",
            "📊 Position sizes are calculated mathematically using Kelly Criterion",
            "⏰ Trading is automatically suspended during high volatility periods",
            "💰 Daily loss limits prevent catastrophic losses",
            "📚 Every decision includes educational explanations for learning",
            "🎯 Paper trading is recommended for new users to practice safely"
        ]
    
    def _generate_educational_summary(self, strategy: ProfitTargetedStrategy,
                                    analyses: List[Dict]) -> str:
        """Generate educational summary"""
        
        return f"""
📚 EDUCATIONAL SUMMARY

🧠 WHAT IS CHAIN-OF-THOUGHT TRADING?
Chain-of-Thought trading means the AI explains every step of its reasoning, just like a human trader would think through a decision. Instead of just saying "buy this stock," it explains WHY, HOW MUCH, and WHAT COULD GO WRONG.

🔍 THE TTM SQUEEZE PATTERN:
This strategy looks for stocks that are "coiled like a spring" - when Bollinger Bands squeeze inside Keltner Channels, it often leads to explosive moves. Think of it like a rubber band being stretched tight.

💰 POSITION SIZING SCIENCE:
We use the Kelly Criterion, a mathematical formula that tells us the optimal bet size to grow your account while minimizing risk of ruin. It's like having a mathematical advisor for every trade.

🎯 WHY THIS APPROACH WORKS:
• Combines proven technical patterns with mathematical position sizing
• Provides transparency in every decision
• Includes multiple safety layers
• Focuses on education, not just profits

Remember: The goal isn't just to make money, but to learn and improve as a trader!
        """.strip()
    
    def _generate_execution_educational_summary(self, cot_analysis: ChainOfThoughtAnalysis,
                                              position_calc: PositionSizeCalculation,
                                              execution: LiveTradeExecution) -> str:
        """Generate educational summary for execution"""
        
        return f"""
🎓 WHAT JUST HAPPENED - EDUCATIONAL BREAKDOWN

🔍 ANALYSIS PROCESS:
The AI analyzed {cot_analysis.symbol} through {len(cot_analysis.steps)} logical steps:
{chr(10).join([f"• {step.title}: {step.explanation[:100]}..." for step in cot_analysis.steps[:3]])}

💰 POSITION SIZING LOGIC:
{position_calc.educational_explanation}

🎯 EXECUTION DETAILS:
{execution.educational_explanation}

💡 KEY LEARNING POINTS:
• Chain-of-thought analysis provides transparency in decision-making
• Mathematical position sizing optimizes risk-adjusted returns
• Automatic protective orders manage downside risk
• Real-time monitoring ensures disciplined execution

This is how professional traders think - systematically, mathematically, and with proper risk management!
        """.strip()
    
    def _generate_portfolio_educational_insights(self, monitoring: PortfolioMonitoring) -> List[str]:
        """Generate educational insights based on portfolio performance"""
        
        insights = []
        
        if monitoring.daily_pnl > 0:
            insights.append("📈 Profitable day! Remember that consistency matters more than big wins")
            insights.append("💡 Consider taking some profits if you're up significantly")
        else:
            insights.append("📉 Red days are normal in trading - your risk management is protecting you")
            insights.append("🎯 Focus on process, not just results - good decisions lead to good outcomes")
        
        if monitoring.open_positions > 4:
            insights.append("📊 Multiple positions provide diversification but require more monitoring")
        
        if abs(monitoring.daily_pnl_percent) > 2:
            insights.append("⚡ High volatility day - consider reducing position sizes")
        
        return insights
    
    def _check_market_hours(self) -> Dict[str, Any]:
        """Check market hours and trading windows"""
        
        now = datetime.now().time()
        
        return {
            "market_open": now >= datetime.strptime("09:30", "%H:%M").time(),
            "safe_trading_window": datetime.strptime("10:00", "%H:%M").time() <= now <= datetime.strptime("15:30", "%H:%M").time(),
            "market_close_soon": now >= datetime.strptime("15:30", "%H:%M").time()
        }
    
    def _calculate_diversification_score(self) -> float:
        """Calculate portfolio diversification score"""
        # Simplified calculation - would be more sophisticated
        return 75.0  # Placeholder
    
    def _check_safety_status(self, monitoring: PortfolioMonitoring) -> Dict[str, Any]:
        """Check overall safety status"""
        
        safety_score = 100.0
        issues = []
        
        if abs(monitoring.daily_pnl_percent) > 2:
            safety_score -= 20
            issues.append("High daily volatility")
        
        if monitoring.open_positions > 5:
            safety_score -= 10
            issues.append("High position count")
        
        return {
            "safety_score": max(safety_score, 0),
            "status": "SAFE" if safety_score >= 80 else "CAUTION" if safety_score >= 60 else "WARNING",
            "issues": issues
        }
    
    def _suggest_next_actions(self, monitoring: PortfolioMonitoring, 
                            market_conditions: Dict[str, Any]) -> List[str]:
        """Suggest next actions based on current state"""
        
        actions = []
        
        if not market_conditions["trading_allowed"]:
            actions.append("⏸️ Wait for better market conditions before new trades")
        elif monitoring.daily_pnl_percent < -2:
            actions.append("🛑 Consider stopping trading for today - preserve capital")
        elif monitoring.open_positions == 0:
            actions.append("🔍 Scan for new opportunities using Chain-of-Thought analysis")
        else:
            actions.append("👀 Monitor existing positions and wait for clear signals")
        
        return actions
