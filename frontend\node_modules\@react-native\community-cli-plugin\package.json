{"name": "@react-native/community-cli-plugin", "version": "0.80.0", "description": "Core CLI commands for React Native", "keywords": ["react-native", "tools"], "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/community-cli-plugin#readme", "bugs": "https://github.com/facebook/react-native/issues", "repository": {"type": "git", "url": "git+https://github.com/facebook/react-native.git", "directory": "packages/community-cli-plugin"}, "license": "MIT", "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "files": ["dist"], "dependencies": {"@react-native/dev-middleware": "0.80.0", "chalk": "^4.0.0", "debug": "^4.4.0", "invariant": "^2.2.4", "metro": "^0.82.2", "metro-config": "^0.82.2", "metro-core": "^0.82.2", "semver": "^7.1.3"}, "devDependencies": {"metro-resolver": "^0.82.2"}, "peerDependencies": {"@react-native-community/cli": "*"}, "peerDependenciesMeta": {"@react-native-community/cli": {"optional": true}}, "engines": {"node": ">=18"}}