"""
Enhanced A.T.L.A.S RAG System Test Suite
Comprehensive testing for expanded trading knowledge base
"""

import asyncio
import time
import logging
from typing import Dict, List, Any

from .trading_books_rag import TradingEducationRAG
from .ai_services import AIServices
from .validation_engine import TradingValidationEngine, ValidationResult


class EnhancedRAGTestSuite:
    """Test suite for enhanced RAG education system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.rag_system = TradingEducationRAG()
        self.ai_services = AIServices()
        self.validation_engine = TradingValidationEngine()
        self.test_results = []
    
    async def run_comprehensive_tests(self):
        """Run all enhanced RAG tests"""
        print("🚀 Starting Enhanced A.T.L.A.S RAG System Tests")
        print("=" * 60)
        
        # Test categories
        await self.test_options_trading_queries()
        await self.test_advanced_ttm_squeeze_queries()
        await self.test_day_trading_queries()
        await self.test_risk_management_queries()
        await self.test_advanced_patterns_queries()
        await self.test_quantitative_strategies()
        await self.test_validation_integration()
        await self.test_response_quality()
        
        # Generate comprehensive report
        self.generate_enhanced_report()
    
    async def test_options_trading_queries(self):
        """Test options trading knowledge"""
        print("\n📊 Testing Options Trading Knowledge...")
        
        options_queries = [
            "Explain the Greeks in options trading and their importance",
            "How do I set up a bull put spread on AAPL?",
            "What is an iron condor and when should I use it?",
            "How does implied volatility affect options pricing?",
            "What are the best credit spread strategies for income?",
            "Explain delta hedging and gamma risk",
            "How do I manage a losing credit spread position?",
            "What is IV rank and how do I use it for options trading?"
        ]
        
        for query in options_queries:
            await self._test_single_query(query, "options_trading", expected_concepts=[
                "delta", "gamma", "theta", "vega", "implied_volatility", 
                "credit_spreads", "iron_condor", "iv_rank"
            ])
    
    async def test_advanced_ttm_squeeze_queries(self):
        """Test advanced TTM Squeeze knowledge"""
        print("\n🔥 Testing Advanced TTM Squeeze Knowledge...")
        
        ttm_queries = [
            "What are the different types of TTM Squeeze setups?",
            "How do I trade multi-timeframe squeeze alignments?",
            "Explain baby squeeze vs monster squeeze patterns",
            "What is a failed squeeze and how do I trade it?",
            "How do I use momentum oscillator with TTM Squeeze?",
            "What are pre-squeeze and post-squeeze entry techniques?",
            "How do I identify squeeze momentum divergence?",
            "What position sizing should I use for triple squeeze setups?"
        ]
        
        for query in ttm_queries:
            await self._test_single_query(query, "ttm_squeeze", expected_concepts=[
                "ttm_squeeze", "momentum_oscillator", "multi_timeframe", 
                "baby_squeeze", "monster_squeeze", "failed_squeeze"
            ])
    
    async def test_day_trading_queries(self):
        """Test day trading strategies"""
        print("\n⚡ Testing Day Trading Knowledge...")
        
        day_trading_queries = [
            "How do I trade opening range breakouts effectively?",
            "What are the best gap trading strategies?",
            "Explain VWAP trading and its applications",
            "How do I scalp 5-minute TTM Squeeze setups?",
            "What are the key risk management rules for day trading?",
            "How do I identify momentum scalping opportunities?",
            "What volume patterns should I look for in day trading?",
            "How do I manage multiple day trading positions?"
        ]
        
        for query in day_trading_queries:
            await self._test_single_query(query, "day_trading", expected_concepts=[
                "opening_range_breakout", "gap_trading", "vwap", 
                "scalping", "momentum", "volume_analysis"
            ])
    
    async def test_risk_management_queries(self):
        """Test enhanced risk management knowledge"""
        print("\n🛡️ Testing Risk Management Knowledge...")
        
        risk_queries = [
            "What are the specific risk management rules for options trading?",
            "How do I calculate position size for credit spreads?",
            "What is the maximum risk I should take per trade?",
            "How do I manage risk in multi-timeframe trading?",
            "What are the key risk metrics for portfolio management?",
            "How do I handle risk during high volatility periods?",
            "What stop-loss strategies work best for different timeframes?",
            "How do I manage correlation risk in my portfolio?"
        ]
        
        for query in risk_queries:
            await self._test_single_query(query, "risk_management", expected_concepts=[
                "position_sizing", "stop_loss", "risk_per_trade", 
                "portfolio_risk", "correlation", "volatility"
            ])
    
    async def test_advanced_patterns_queries(self):
        """Test advanced pattern recognition"""
        print("\n📈 Testing Advanced Pattern Knowledge...")
        
        pattern_queries = [
            "Explain Wyckoff accumulation and distribution patterns",
            "What are order blocks and how do I identify them?",
            "How do I trade fair value gaps in price action?",
            "What is smart money concept in trading?",
            "How do I identify institutional order flow?",
            "What are the phases of Wyckoff accumulation?",
            "How do I spot liquidity pools and hunt levels?",
            "What is market structure and break of structure?"
        ]
        
        for query in pattern_queries:
            await self._test_single_query(query, "advanced_patterns", expected_concepts=[
                "wyckoff", "order_blocks", "fair_value_gaps", "smart_money",
                "institutional_flow", "market_structure", "liquidity_pools"
            ])
    
    async def test_quantitative_strategies(self):
        """Test quantitative trading knowledge"""
        print("\n🔢 Testing Quantitative Strategy Knowledge...")
        
        quant_queries = [
            "How do I implement mean reversion strategies?",
            "What is pairs trading and how does it work?",
            "How do I backtest trading strategies effectively?",
            "What are the key statistical measures for trading systems?",
            "How do I identify relative strength momentum?",
            "What filters should I use for breakout systems?",
            "How do I calculate correlation for pairs trading?",
            "What are the requirements for systematic trading?"
        ]
        
        for query in quant_queries:
            await self._test_single_query(query, "quantitative", expected_concepts=[
                "mean_reversion", "pairs_trading", "backtesting", 
                "correlation", "relative_strength", "systematic_trading"
            ])
    
    async def test_validation_integration(self):
        """Test validation engine with new concepts"""
        print("\n✅ Testing Validation Integration...")
        
        validation_tests = [
            {
                "response": "Buy AAPL $150 calls with 30 DTE when IV rank is 25%",
                "should_pass": False,
                "reason": "Low IV rank for buying options"
            },
            {
                "response": "Sell TSLA bull put spread with IV rank 60% and 30 DTE",
                "should_pass": True,
                "reason": "Good conditions for credit spread"
            },
            {
                "response": "Trade opening range breakout with 2.5x volume confirmation",
                "should_pass": True,
                "reason": "Valid ORB setup"
            },
            {
                "response": "Enter iron condor with 8-point profit zone on high IV",
                "should_pass": False,
                "reason": "Profit zone too narrow"
            }
        ]
        
        for test in validation_tests:
            result, reason, details = await self.validation_engine.validate_ai_response(
                test["response"], {"symbol": "AAPL", "current_price": 150}
            )
            
            passed = (result == ValidationResult.VALID) == test["should_pass"]
            self._log_test(
                f"Validation: {test['response'][:30]}...",
                passed,
                f"Expected: {test['should_pass']}, Got: {result.value}, Reason: {reason}"
            )
    
    async def test_response_quality(self):
        """Test overall response quality and accuracy"""
        print("\n🎯 Testing Response Quality...")
        
        quality_tests = [
            {
                "query": "How do I manage a losing iron condor position?",
                "expected_elements": ["roll", "close", "defend", "adjustment"],
                "min_length": 200
            },
            {
                "query": "What are the phases of TTM Squeeze?",
                "expected_elements": ["compression", "expansion", "momentum", "direction"],
                "min_length": 150
            },
            {
                "query": "Explain delta hedging for options portfolios",
                "expected_elements": ["delta", "hedge", "neutral", "adjustment"],
                "min_length": 180
            }
        ]
        
        for test in quality_tests:
            start_time = time.time()
            response = await self.rag_system.answer_education_query(test["query"])
            duration = time.time() - start_time
            
            # Check response quality
            has_elements = all(elem.lower() in response.answer.lower() 
                             for elem in test["expected_elements"])
            sufficient_length = len(response.answer) >= test["min_length"]
            fast_response = duration < 5.0
            
            quality_score = sum([has_elements, sufficient_length, fast_response]) / 3
            
            self._log_test(
                f"Quality: {test['query'][:30]}...",
                quality_score >= 0.8,
                f"Score: {quality_score:.2f}, Duration: {duration:.2f}s, Length: {len(response.answer)}"
            )
    
    async def _test_single_query(self, query: str, category: str, expected_concepts: List[str] = None):
        """Test a single query and validate response"""
        try:
            start_time = time.time()
            
            # Get RAG response
            response = await self.rag_system.answer_education_query(query)
            duration = time.time() - start_time
            
            # Validate response
            validation_result, validation_reason, _ = await self.validation_engine.validate_ai_response(
                response.answer, {}
            )
            
            # Check for expected concepts
            concepts_found = 0
            if expected_concepts:
                for concept in expected_concepts:
                    if concept.lower() in response.answer.lower():
                        concepts_found += 1
                concept_score = concepts_found / len(expected_concepts)
            else:
                concept_score = 1.0
            
            # Overall success criteria
            success = (
                duration < 5.0 and
                len(response.answer) > 100 and
                validation_result != ValidationResult.INVALID and
                concept_score >= 0.5
            )
            
            self._log_test(
                f"{category.title()}: {query[:40]}...",
                success,
                f"Duration: {duration:.2f}s, Concepts: {concepts_found}/{len(expected_concepts) if expected_concepts else 'N/A'}, Validation: {validation_result.value}"
            )
            
        except Exception as e:
            self._log_test(
                f"{category.title()}: {query[:40]}...",
                False,
                f"Error: {e}"
            )
    
    def _log_test(self, test_name: str, success: bool, details: str):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} {test_name}")
        if not success or "FAIL" in details:
            print(f"      {details}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details
        })
    
    def generate_enhanced_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📊 ENHANCED A.T.L.A.S RAG SYSTEM TEST REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r["success"])
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n📈 Overall Results:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {total_tests - passed_tests}")
        print(f"   Success Rate: {success_rate:.1f}%")
        
        # Category breakdown
        categories = {}
        for result in self.test_results:
            category = result["test"].split(":")[0]
            if category not in categories:
                categories[category] = {"total": 0, "passed": 0}
            categories[category]["total"] += 1
            if result["success"]:
                categories[category]["passed"] += 1
        
        print(f"\n🎯 Category Breakdown:")
        for category, stats in categories.items():
            rate = (stats["passed"] / stats["total"] * 100) if stats["total"] > 0 else 0
            status = "✅" if rate >= 80 else "⚠️" if rate >= 60 else "❌"
            print(f"   {status} {category}: {stats['passed']}/{stats['total']} ({rate:.1f}%)")
        
        # Enhanced features status
        print(f"\n🚀 Enhanced Features Status:")
        enhanced_features = [
            "Options Trading Knowledge",
            "Advanced TTM Squeeze Techniques", 
            "Day Trading Strategies",
            "Risk Management for Derivatives",
            "Advanced Pattern Recognition",
            "Quantitative Trading Methods",
            "Validation Integration",
            "Response Quality Assurance"
        ]
        
        for feature in enhanced_features:
            feature_tests = [r for r in self.test_results if feature.lower().replace(" ", "_") in r["test"].lower()]
            if feature_tests:
                feature_success = sum(1 for r in feature_tests if r["success"]) / len(feature_tests)
                status = "✅" if feature_success >= 0.8 else "⚠️" if feature_success >= 0.6 else "❌"
                print(f"   {status} {feature}")
            else:
                print(f"   ⚪ {feature} (No specific tests)")
        
        print(f"\n🎉 Enhanced A.T.L.A.S RAG System: {'READY FOR PRODUCTION' if success_rate >= 80 else 'NEEDS IMPROVEMENT'}")


# Main execution
async def main():
    """Run enhanced RAG tests"""
    test_suite = EnhancedRAGTestSuite()
    await test_suite.run_comprehensive_tests()


if __name__ == "__main__":
    asyncio.run(main())
