import{applyProps as t}from"react-zdog";import{Globals as r}from"@react-spring/core";import{createStringInterpolator as p,colors as n}from"@react-spring/shared";import{createHost as d}from"@react-spring/animated";import*as o from"react-zdog";var e={Anchor:o.<PERSON>chor,Shape:<PERSON><PERSON>,Group:o.Group,Rect:o.Rect,RoundedRect:o.RoundedRect,Ellipse:o.Ellipse,Polygon:o.Polygon,Hemisphere:o.Hemisphere,Cylinder:o.<PERSON>linder,Cone:o.<PERSON>e,Box:o.Box};export*from"@react-spring/core";r.assign({createStringInterpolator:p,colors:n});var i=d(e,{applyAnimatedValues:t}),y=i.animated;export{y as a,y as animated};
//# sourceMappingURL=react-spring_zdog.modern.production.min.mjs.map