"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),o=require("@react-three/fiber"),n=require("@react-spring/three"),a=require("@use-gesture/react");function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=s(e),c=u(t);exports.PresentationControls=function({enabled:e=!0,snap:t,global:s,domElement:u,cursor:l=!0,children:f,speed:d=1,rotation:m=[0,0,0],zoom:g=1,polar:h=[0,Math.PI/2],azimuth:b=[-1/0,1/0],config:p={mass:1,tension:170,friction:26}}){const y=o.useThree((e=>e.events)),M=o.useThree((e=>e.gl)),v=u||y.connected||M.domElement,{size:E}=o.useThree(),j=c.useMemo((()=>[m[0]+h[0],m[0]+h[1]]),[m[0],h[0],h[1]]),O=c.useMemo((()=>[m[1]+b[0],m[1]+b[1]]),[m[1],b[0],b[1]]),P=c.useMemo((()=>[r.MathUtils.clamp(m[0],...j),r.MathUtils.clamp(m[1],...O),m[2]]),[m[0],m[1],m[2],j,O]),[q,z]=n.useSpring((()=>({scale:1,rotation:P,config:p})));c.useEffect((()=>{z.start({scale:1,rotation:P,config:p})}),[P]),c.useEffect((()=>{if(s&&l&&e)return v.style.cursor="grab",M.domElement.style.cursor="",()=>{v.style.cursor="default",M.domElement.style.cursor="default"}}),[s,l,v,e]);const U=a.useGesture({onHover:({last:t})=>{l&&!s&&e&&(v.style.cursor=t?"auto":"grab")},onDrag:({down:o,delta:[n,a],memo:[s,u]=q.rotation.animation.to||P})=>{if(!e)return[a,n];l&&(v.style.cursor=o?"grabbing":"grab"),n=r.MathUtils.clamp(u+n/E.width*Math.PI*d,...O),a=r.MathUtils.clamp(s+a/E.height*Math.PI*d,...j);const i=t&&!o&&"boolean"!=typeof t?t:p;return z.start({scale:o&&a>j[1]/2?g:1,rotation:t&&!o?P:[a,n,0],config:e=>"scale"===e?{...i,friction:3*i.friction}:i}),[a,n]}},{target:s?v:void 0});return c.createElement(n.a.group,i.default({},null==U?void 0:U(),q),f)};
