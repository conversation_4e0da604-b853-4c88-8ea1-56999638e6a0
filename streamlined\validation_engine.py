"""
Enhanced A.T.L.A.S Trading System - Validation Engine
Prevent trading logic hallucinations and ensure response accuracy
"""

import re
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import json

from .models import TradingSignal, Quote, TechnicalIndicators
from .config import settings


class ValidationLevel(Enum):
    STRICT = "strict"
    MODERATE = "moderate"
    PERMISSIVE = "permissive"


class ValidationResult(Enum):
    VALID = "valid"
    WARNING = "warning"
    INVALID = "invalid"


class TradingValidationEngine:
    """Comprehensive validation engine for trading recommendations and AI responses"""
    
    def __init__(self, validation_level: ValidationLevel = ValidationLevel.MODERATE):
        self.logger = logging.getLogger(__name__)
        self.validation_level = validation_level
        self.valid_indicators = self._load_valid_indicators()
        self.valid_strategies = self._load_valid_strategies()
        self.validation_history = []
    
    def _load_valid_indicators(self) -> Dict[str, Dict[str, Any]]:
        """Load whitelist of valid technical indicators with their parameters"""
        return {
            "rsi": {
                "name": "Relative Strength Index",
                "range": [0, 100],
                "oversold": 30,
                "overbought": 70,
                "typical_period": 14
            },
            "macd": {
                "name": "Moving Average Convergence Divergence",
                "range": [-float('inf'), float('inf')],
                "components": ["macd_line", "signal_line", "histogram"],
                "typical_periods": [12, 26, 9]
            },
            "bollinger_bands": {
                "name": "Bollinger Bands",
                "components": ["upper", "middle", "lower"],
                "typical_period": 20,
                "typical_std": 2.0
            },
            "ema": {
                "name": "Exponential Moving Average",
                "range": [0, float('inf')],
                "common_periods": [8, 12, 21, 26, 50, 200]
            },
            "sma": {
                "name": "Simple Moving Average",
                "range": [0, float('inf')],
                "common_periods": [20, 50, 200]
            },
            "atr": {
                "name": "Average True Range",
                "range": [0, float('inf')],
                "typical_period": 14
            },
            "volume": {
                "name": "Volume",
                "range": [0, float('inf')],
                "ratios": ["volume_ratio", "volume_sma"]
            },
            "support_resistance": {
                "name": "Support and Resistance",
                "components": ["support_level", "resistance_level"],
                "validation": "price_based"
            },
            "options": {
                "name": "Options Trading",
                "components": ["calls", "puts", "strike_price", "expiration", "premium"],
                "greeks": ["delta", "gamma", "theta", "vega"],
                "strategies": ["credit_spreads", "iron_condor", "bull_put_spread", "bear_call_spread"]
            },
            "implied_volatility": {
                "name": "Implied Volatility",
                "range": [0, 200],
                "components": ["iv_rank", "iv_percentile", "volatility_crush"],
                "typical_range": [10, 80]
            },
            "order_flow": {
                "name": "Order Flow Analysis",
                "components": ["order_blocks", "fair_value_gaps", "liquidity_pools", "inducement"],
                "validation": "institutional_patterns"
            }
        }
    
    def _load_valid_strategies(self) -> Dict[str, Dict[str, Any]]:
        """Load whitelist of valid trading strategies"""
        return {
            "ttm_squeeze": {
                "name": "TTM Squeeze",
                "required_indicators": ["bollinger_bands", "atr", "volume"],
                "entry_conditions": ["squeeze_active", "momentum_direction", "volume_confirmation"],
                "risk_management": "required"
            },
            "ema_crossover": {
                "name": "EMA Crossover",
                "required_indicators": ["ema", "volume", "rsi"],
                "entry_conditions": ["ema_cross", "volume_confirmation", "trend_alignment"],
                "risk_management": "required"
            },
            "breakout": {
                "name": "Breakout Trading",
                "required_indicators": ["support_resistance", "volume", "rsi"],
                "entry_conditions": ["resistance_break", "volume_spike", "momentum_confirmation"],
                "risk_management": "required"
            },
            "rsi_divergence": {
                "name": "RSI Divergence",
                "required_indicators": ["rsi", "price_action", "volume"],
                "entry_conditions": ["divergence_confirmed", "oversold_overbought", "volume_confirmation"],
                "risk_management": "required"
            },
            "support_bounce": {
                "name": "Support Bounce",
                "required_indicators": ["support_resistance", "rsi", "volume"],
                "entry_conditions": ["support_hold", "oversold_condition", "volume_confirmation"],
                "risk_management": "required"
            },
            "credit_spreads": {
                "name": "Credit Spreads",
                "required_indicators": ["implied_volatility", "delta", "time_decay"],
                "entry_conditions": ["high_iv_rank", "delta_selection", "dte_range"],
                "risk_management": "required",
                "options_specific": True
            },
            "iron_condor": {
                "name": "Iron Condor",
                "required_indicators": ["implied_volatility", "bollinger_bands", "support_resistance"],
                "entry_conditions": ["range_bound", "high_iv", "wide_profit_zone"],
                "risk_management": "required",
                "options_specific": True
            },
            "opening_range_breakout": {
                "name": "Opening Range Breakout",
                "required_indicators": ["volume", "opening_range", "momentum"],
                "entry_conditions": ["range_break", "volume_confirmation", "follow_through"],
                "risk_management": "required",
                "day_trading": True
            },
            "gap_trading": {
                "name": "Gap Trading",
                "required_indicators": ["gap_size", "volume", "news_catalyst"],
                "entry_conditions": ["significant_gap", "volume_confirmation", "direction_bias"],
                "risk_management": "required",
                "day_trading": True
            },
            "wyckoff_accumulation": {
                "name": "Wyckoff Accumulation",
                "required_indicators": ["volume", "support_resistance", "relative_strength"],
                "entry_conditions": ["spring_test", "sign_of_strength", "last_point_support"],
                "risk_management": "required",
                "institutional": True
            }
        }
    
    async def validate_ai_response(self, response: str, context: Dict[str, Any] = None) -> Tuple[ValidationResult, str, Dict[str, Any]]:
        """Validate AI response for trading accuracy and prevent hallucinations"""
        try:
            validation_results = {
                "timestamp": datetime.utcnow().isoformat(),
                "response_length": len(response),
                "context_provided": bool(context),
                "checks_performed": []
            }
            
            # Check 1: Validate technical indicator mentions
            indicator_check = self._validate_indicator_mentions(response)
            validation_results["checks_performed"].append(indicator_check)
            
            # Check 2: Validate strategy mentions
            strategy_check = self._validate_strategy_mentions(response)
            validation_results["checks_performed"].append(strategy_check)
            
            # Check 3: Validate numerical claims
            numerical_check = self._validate_numerical_claims(response, context)
            validation_results["checks_performed"].append(numerical_check)
            
            # Check 4: Validate trading recommendations
            recommendation_check = self._validate_trading_recommendations(response)
            validation_results["checks_performed"].append(recommendation_check)
            
            # Check 5: Validate risk management mentions
            risk_check = self._validate_risk_management(response)
            validation_results["checks_performed"].append(risk_check)
            
            # Determine overall validation result
            overall_result, overall_reason = self._determine_overall_result(validation_results["checks_performed"])
            
            # Log validation
            self.validation_history.append({
                "timestamp": datetime.utcnow(),
                "result": overall_result.value,
                "reason": overall_reason,
                "response_preview": response[:100] + "..." if len(response) > 100 else response
            })
            
            return overall_result, overall_reason, validation_results
            
        except Exception as e:
            self.logger.error(f"Error validating AI response: {e}")
            return ValidationResult.INVALID, f"Validation system error: {e}", {}
    
    def _validate_indicator_mentions(self, response: str) -> Dict[str, Any]:
        """Validate that mentioned technical indicators are real and used correctly"""
        check_result = {
            "check_name": "indicator_validation",
            "status": "pass",
            "issues": [],
            "warnings": []
        }
        
        response_lower = response.lower()
        
        # Check for valid indicators
        mentioned_indicators = []
        for indicator, details in self.valid_indicators.items():
            if indicator in response_lower or details["name"].lower() in response_lower:
                mentioned_indicators.append(indicator)
        
        # Check for invalid indicator claims
        invalid_patterns = [
            r"rsi.*(?:above|over|greater than)\s*100",
            r"rsi.*(?:below|under|less than)\s*0",
            r"macd.*(?:above|over)\s*1000",
            r"volume.*negative",
            r"price.*negative",
            r"(?:support|resistance).*negative"
        ]
        
        for pattern in invalid_patterns:
            if re.search(pattern, response_lower):
                check_result["issues"].append(f"Invalid indicator claim detected: {pattern}")
                check_result["status"] = "fail"
        
        # Check RSI range validity
        rsi_values = re.findall(r"rsi.*?(\d+(?:\.\d+)?)", response_lower)
        for value in rsi_values:
            try:
                rsi_val = float(value)
                if rsi_val < 0 or rsi_val > 100:
                    check_result["issues"].append(f"Invalid RSI value: {rsi_val} (must be 0-100)")
                    check_result["status"] = "fail"
            except ValueError:
                continue
        
        check_result["mentioned_indicators"] = mentioned_indicators
        return check_result
    
    def _validate_strategy_mentions(self, response: str) -> Dict[str, Any]:
        """Validate that mentioned trading strategies are legitimate"""
        check_result = {
            "check_name": "strategy_validation",
            "status": "pass",
            "issues": [],
            "warnings": []
        }
        
        response_lower = response.lower()
        
        # Check for valid strategies
        mentioned_strategies = []
        for strategy, details in self.valid_strategies.items():
            if strategy in response_lower or details["name"].lower() in response_lower:
                mentioned_strategies.append(strategy)
        
        # Check for invalid strategy claims
        invalid_strategy_patterns = [
            r"guaranteed.*profit",
            r"100%.*success",
            r"never.*lose",
            r"risk.*free",
            r"always.*works",
            r"perfect.*strategy"
        ]
        
        for pattern in invalid_strategy_patterns:
            if re.search(pattern, response_lower):
                check_result["issues"].append(f"Invalid strategy claim: {pattern}")
                check_result["status"] = "fail"
        
        check_result["mentioned_strategies"] = mentioned_strategies
        return check_result
    
    def _validate_numerical_claims(self, response: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Validate numerical claims against actual market data"""
        check_result = {
            "check_name": "numerical_validation",
            "status": "pass",
            "issues": [],
            "warnings": []
        }
        
        if not context:
            check_result["warnings"].append("No context provided for numerical validation")
            return check_result
        
        # Extract price mentions
        price_mentions = re.findall(r"\$(\d+(?:\.\d+)?)", response)
        current_price = context.get("current_price")
        
        if current_price and price_mentions:
            for price_str in price_mentions:
                try:
                    mentioned_price = float(price_str)
                    # Check if mentioned price is reasonable (within 50% of current price)
                    price_diff_percent = abs(mentioned_price - current_price) / current_price * 100
                    
                    if price_diff_percent > 50:
                        check_result["warnings"].append(
                            f"Mentioned price ${mentioned_price} differs significantly from current ${current_price:.2f}"
                        )
                except ValueError:
                    continue
        
        # Validate RSI claims against actual data
        rsi_actual = context.get("rsi")
        rsi_mentions = re.findall(r"rsi.*?(\d+(?:\.\d+)?)", response.lower())
        
        if rsi_actual and rsi_mentions:
            for rsi_str in rsi_mentions:
                try:
                    mentioned_rsi = float(rsi_str)
                    if abs(mentioned_rsi - rsi_actual) > 10:
                        check_result["warnings"].append(
                            f"Mentioned RSI {mentioned_rsi} differs from actual {rsi_actual:.1f}"
                        )
                except ValueError:
                    continue
        
        return check_result
    
    def _validate_trading_recommendations(self, response: str) -> Dict[str, Any]:
        """Validate trading recommendations for completeness and safety"""
        check_result = {
            "check_name": "recommendation_validation",
            "status": "pass",
            "issues": [],
            "warnings": []
        }
        
        response_lower = response.lower()
        
        # Check for trading action words
        action_words = ["buy", "sell", "enter", "exit", "long", "short"]
        has_trading_action = any(word in response_lower for word in action_words)
        
        if has_trading_action:
            # If trading action is mentioned, check for risk management
            risk_words = ["stop", "loss", "risk", "position size", "exit"]
            has_risk_management = any(word in response_lower for word in risk_words)
            
            if not has_risk_management:
                check_result["issues"].append("Trading recommendation lacks risk management guidance")
                check_result["status"] = "fail"
            
            # Check for unrealistic claims
            unrealistic_patterns = [
                r"(?:guaranteed|certain|sure).*profit",
                r"no.*risk",
                r"100%.*(?:success|win)",
                r"easy.*money",
                r"quick.*rich"
            ]
            
            for pattern in unrealistic_patterns:
                if re.search(pattern, response_lower):
                    check_result["issues"].append(f"Unrealistic trading claim: {pattern}")
                    check_result["status"] = "fail"
        
        return check_result
    
    def _validate_risk_management(self, response: str) -> Dict[str, Any]:
        """Validate risk management content"""
        check_result = {
            "check_name": "risk_management_validation",
            "status": "pass",
            "issues": [],
            "warnings": []
        }
        
        response_lower = response.lower()
        
        # Check for proper risk management language
        good_risk_phrases = [
            "stop loss", "position size", "risk management", "cut losses",
            "risk per trade", "money management", "exit strategy"
        ]
        
        bad_risk_phrases = [
            "no stop loss", "all in", "bet everything", "mortgage the house",
            "guaranteed", "risk free", "can't lose"
        ]
        
        has_good_risk = any(phrase in response_lower for phrase in good_risk_phrases)
        has_bad_risk = any(phrase in response_lower for phrase in bad_risk_phrases)
        
        if has_bad_risk:
            check_result["issues"].append("Contains dangerous risk management advice")
            check_result["status"] = "fail"
        
        # Check for specific risk percentages
        risk_percentages = re.findall(r"(\d+(?:\.\d+)?)%.*risk", response_lower)
        for risk_str in risk_percentages:
            try:
                risk_percent = float(risk_str)
                if risk_percent > 10:
                    check_result["warnings"].append(f"High risk percentage mentioned: {risk_percent}%")
                elif risk_percent > 20:
                    check_result["issues"].append(f"Dangerously high risk percentage: {risk_percent}%")
                    check_result["status"] = "fail"
            except ValueError:
                continue
        
        return check_result
    
    def _determine_overall_result(self, checks: List[Dict[str, Any]]) -> Tuple[ValidationResult, str]:
        """Determine overall validation result from individual checks"""
        failed_checks = [check for check in checks if check["status"] == "fail"]
        warning_checks = [check for check in checks if check.get("warnings")]
        
        if failed_checks:
            failed_names = [check["check_name"] for check in failed_checks]
            all_issues = []
            for check in failed_checks:
                all_issues.extend(check.get("issues", []))
            
            return ValidationResult.INVALID, f"Failed checks: {', '.join(failed_names)}. Issues: {'; '.join(all_issues[:3])}"
        
        elif warning_checks and self.validation_level == ValidationLevel.STRICT:
            warning_names = [check["check_name"] for check in warning_checks]
            return ValidationResult.WARNING, f"Warnings in: {', '.join(warning_names)}"
        
        elif warning_checks:
            return ValidationResult.WARNING, "Minor warnings detected but response is acceptable"
        
        else:
            return ValidationResult.VALID, "All validation checks passed"
    
    async def validate_trading_signal(self, signal: TradingSignal, market_context: Dict[str, Any] = None) -> Tuple[ValidationResult, str]:
        """Validate a trading signal for safety and accuracy"""
        try:
            issues = []
            warnings = []
            
            # Validate signal completeness
            if not signal.entry_price or signal.entry_price <= 0:
                issues.append("Invalid entry price")
            
            if not signal.stop_loss or signal.stop_loss <= 0:
                issues.append("Missing or invalid stop loss")
            
            # Validate risk-reward ratio
            if signal.target_price and signal.stop_loss and signal.entry_price:
                if signal.signal_type.value == "buy":
                    risk = signal.entry_price - signal.stop_loss
                    reward = signal.target_price - signal.entry_price
                else:
                    risk = signal.stop_loss - signal.entry_price
                    reward = signal.entry_price - signal.target_price
                
                if risk <= 0:
                    issues.append("Invalid risk calculation")
                elif reward <= 0:
                    issues.append("Invalid reward calculation")
                else:
                    rr_ratio = reward / risk
                    if rr_ratio < 1:
                        warnings.append(f"Poor risk-reward ratio: {rr_ratio:.2f}")
                    elif rr_ratio < 0.5:
                        issues.append(f"Unacceptable risk-reward ratio: {rr_ratio:.2f}")
            
            # Validate confidence level
            if signal.confidence < 0.3:
                warnings.append(f"Low confidence signal: {signal.confidence:.2f}")
            elif signal.confidence > 1.0:
                issues.append(f"Invalid confidence level: {signal.confidence:.2f}")
            
            # Validate against market context
            if market_context:
                current_price = market_context.get("current_price")
                if current_price:
                    price_diff = abs(signal.entry_price - current_price) / current_price * 100
                    if price_diff > 5:
                        warnings.append(f"Entry price {price_diff:.1f}% away from current price")
            
            # Determine result
            if issues:
                return ValidationResult.INVALID, f"Signal validation failed: {'; '.join(issues)}"
            elif warnings:
                return ValidationResult.WARNING, f"Signal warnings: {'; '.join(warnings)}"
            else:
                return ValidationResult.VALID, "Signal validation passed"
                
        except Exception as e:
            self.logger.error(f"Error validating trading signal: {e}")
            return ValidationResult.INVALID, f"Validation error: {e}"
    
    def get_validation_summary(self, days: int = 7) -> Dict[str, Any]:
        """Get validation summary for recent period"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        recent_validations = [v for v in self.validation_history if v["timestamp"] > cutoff_date]
        
        if not recent_validations:
            return {"message": "No recent validations"}
        
        total = len(recent_validations)
        valid_count = sum(1 for v in recent_validations if v["result"] == "valid")
        warning_count = sum(1 for v in recent_validations if v["result"] == "warning")
        invalid_count = sum(1 for v in recent_validations if v["result"] == "invalid")
        
        return {
            "period_days": days,
            "total_validations": total,
            "valid_responses": valid_count,
            "warning_responses": warning_count,
            "invalid_responses": invalid_count,
            "success_rate": (valid_count / total * 100) if total > 0 else 0,
            "generated_at": datetime.utcnow().isoformat()
        }
