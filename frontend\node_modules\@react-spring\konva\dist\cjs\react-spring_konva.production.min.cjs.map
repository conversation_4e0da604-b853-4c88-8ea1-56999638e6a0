{"version": 3, "sources": ["../../src/index.ts", "../../src/primitives.ts"], "sourcesContent": ["import { Globals, createStringInterpolator, colors } from '@react-spring/shared'\nimport { createHost } from '@react-spring/animated'\nimport { primitives } from './primitives'\nimport { WithAnimated } from './animated'\n\nGlobals.assign({\n  createStringInterpolator,\n  colors,\n})\n\nconst host = createHost(primitives, {\n  applyAnimatedValues(instance, props) {\n    if (!instance.nodeType) return false\n    instance._applyProps(instance, props)\n  },\n})\n\nexport const animated = host.animated as WithAnimated\nexport { animated as a }\n\nexport * from './animated'\nexport * from '@react-spring/core'\n", "import { ElementType } from 'react'\nimport * as konva from 'react-konva'\n\nexport type KonvaExports = typeof konva\n\nexport type Primitives = {\n  [P in keyof KonvaExports]: KonvaExports[P] extends ElementType ? P : never\n}[keyof KonvaExports]\n\nexport const primitives: Primitives[] = [\n  'Arc',\n  'Arrow',\n  'Circle',\n  'Ellipse',\n  'FastLayer',\n  'Group',\n  'Image',\n  'Label',\n  'Layer',\n  'Line',\n  'Path',\n  'Rect',\n  'RegularPolygon',\n  'Ring',\n  'Shape',\n  'Sprite',\n  'Star',\n  'Tag',\n  'Text',\n  'TextPath',\n  'Transformer',\n  'Wedge',\n]\n"], "mappings": "2dAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,OAAAE,EAAA,aAAAA,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAA0D,gCAC1DC,EAA2B,kCCQpB,IAAMC,EAA2B,CACtC,MACA,QACA,SACA,UACA,YACA,QACA,QACA,QACA,QACA,OACA,OACA,OACA,iBACA,OACA,QACA,SACA,OACA,MACA,OACA,WACA,cACA,OACF,EDXAC,EAAAC,EAAc,8BArBd,gBAKA,UAAQ,OAAO,CACb,oDACA,eACF,CAAC,EAED,IAAMC,KAAO,cAAWC,EAAY,CAClC,oBAAoBC,EAAUC,EAAO,CACnC,GAAI,CAACD,EAAS,SAAU,MAAO,GAC/BA,EAAS,YAAYA,EAAUC,CAAK,CACtC,CACF,CAAC,EAEYC,EAAWJ,EAAK", "names": ["src_exports", "__export", "animated", "__toCommonJS", "import_shared", "import_animated", "primitives", "__reExport", "src_exports", "host", "primitives", "instance", "props", "animated"]}