"""
A.T.L.A.S Options Education and Intelligence Engine
Educational components for options trading with beginner-friendly explanations
"""

import logging
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from scipy.stats import norm
import numpy as np

from .config import settings
from .models import OptionsEducationContext, Quote


@dataclass
class OptionsGreeks:
    """Options Greeks with educational explanations"""
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float
    delta_explanation: str
    gamma_explanation: str
    theta_explanation: str
    vega_explanation: str
    rho_explanation: str


@dataclass
class OptionsRiskAssessment:
    """Options-specific risk assessment"""
    iv_rank: float
    iv_percentile: float
    theta_decay_daily: float
    assignment_risk: str
    liquidity_score: float
    earnings_proximity: int  # Days until earnings
    warnings: List[str]
    educational_tips: List[str]


@dataclass
class OptionsEducationalAnalysis:
    """Complete options educational analysis"""
    symbol: str
    option_type: str  # "call" or "put"
    strike: float
    expiration: datetime
    current_price: float
    greeks: OptionsGreeks
    risk_assessment: OptionsRiskAssessment
    beginner_summary: str
    advanced_insights: List[str]


class OptionsEducationEngine:
    """
    Educational engine for options trading with simple explanations and warnings
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Options education analogies
        self.analogies = {
            "delta": "Delta tells you how much your option price moves per $1 stock move - like a speedometer showing sensitivity",
            "gamma": "Gamma shows how much Delta changes - like acceleration, it tells you if Delta is speeding up or slowing down",
            "theta": "Theta is time decay - like ice melting, your option loses value each day just from time passing",
            "vega": "Vega measures volatility sensitivity - like how much a boat rocks based on wave size",
            "iv_crush": "IV crush happens when volatility drops suddenly - like a balloon deflating after being inflated",
            "assignment": "Assignment is when you're forced to buy/sell stock - like being called to fulfill a contract"
        }
    
    def analyze_options_educational(self, 
                                  symbol: str,
                                  option_type: str,
                                  strike: float,
                                  expiration: datetime,
                                  stock_price: float,
                                  option_price: float,
                                  implied_volatility: float,
                                  volume: int = 0,
                                  open_interest: int = 0) -> OptionsEducationalAnalysis:
        """
        Comprehensive educational analysis of an options position
        """
        
        # Calculate Greeks
        greeks = self._calculate_greeks_with_education(
            stock_price, strike, expiration, implied_volatility, option_type
        )
        
        # Risk assessment
        risk_assessment = self._assess_options_risks(
            symbol, option_type, strike, expiration, stock_price, 
            implied_volatility, volume, open_interest
        )
        
        # Generate beginner summary
        beginner_summary = self._generate_beginner_summary(
            symbol, option_type, strike, stock_price, greeks, risk_assessment
        )
        
        # Advanced insights
        advanced_insights = self._generate_advanced_insights(
            option_type, strike, stock_price, greeks, risk_assessment
        )
        
        return OptionsEducationalAnalysis(
            symbol=symbol,
            option_type=option_type,
            strike=strike,
            expiration=expiration,
            current_price=option_price,
            greeks=greeks,
            risk_assessment=risk_assessment,
            beginner_summary=beginner_summary,
            advanced_insights=advanced_insights
        )
    
    def _calculate_greeks_with_education(self, 
                                       stock_price: float,
                                       strike: float,
                                       expiration: datetime,
                                       iv: float,
                                       option_type: str) -> OptionsGreeks:
        """Calculate Greeks with educational explanations"""
        
        # Time to expiration in years
        days_to_exp = (expiration - datetime.now()).days
        time_to_exp = max(days_to_exp / 365.0, 0.001)  # Avoid division by zero
        
        # Risk-free rate (simplified)
        risk_free_rate = 0.05
        
        # Black-Scholes calculations (simplified)
        d1 = (math.log(stock_price / strike) + (risk_free_rate + 0.5 * iv**2) * time_to_exp) / (iv * math.sqrt(time_to_exp))
        d2 = d1 - iv * math.sqrt(time_to_exp)
        
        # Calculate Greeks
        if option_type.lower() == "call":
            delta = norm.cdf(d1)
            gamma = norm.pdf(d1) / (stock_price * iv * math.sqrt(time_to_exp))
            theta = -(stock_price * norm.pdf(d1) * iv) / (2 * math.sqrt(time_to_exp)) - risk_free_rate * strike * math.exp(-risk_free_rate * time_to_exp) * norm.cdf(d2)
            vega = stock_price * norm.pdf(d1) * math.sqrt(time_to_exp)
            rho = strike * time_to_exp * math.exp(-risk_free_rate * time_to_exp) * norm.cdf(d2)
        else:  # put
            delta = norm.cdf(d1) - 1
            gamma = norm.pdf(d1) / (stock_price * iv * math.sqrt(time_to_exp))
            theta = -(stock_price * norm.pdf(d1) * iv) / (2 * math.sqrt(time_to_exp)) + risk_free_rate * strike * math.exp(-risk_free_rate * time_to_exp) * norm.cdf(-d2)
            vega = stock_price * norm.pdf(d1) * math.sqrt(time_to_exp)
            rho = -strike * time_to_exp * math.exp(-risk_free_rate * time_to_exp) * norm.cdf(-d2)
        
        # Convert theta to daily
        theta_daily = theta / 365
        vega = vega / 100  # Convert to per 1% IV change
        
        # Generate educational explanations
        delta_explanation = f"Delta: {delta:.2f} - If {symbol} moves up $1, this option will move ${abs(delta):.2f} {'up' if delta > 0 else 'down'}"
        
        gamma_explanation = f"Gamma: {gamma:.3f} - Delta will change by {gamma:.3f} for each $1 stock move"
        
        theta_explanation = f"Theta: ${theta_daily:.2f}/day - This option loses ${abs(theta_daily):.2f} in value each day from time decay"
        
        vega_explanation = f"Vega: ${vega:.2f} - Option price changes ${abs(vega):.2f} for each 1% change in volatility"
        
        rho_explanation = f"Rho: ${rho:.2f} - Option price changes ${abs(rho):.2f} for each 1% interest rate change"
        
        return OptionsGreeks(
            delta=delta,
            gamma=gamma,
            theta=theta_daily,
            vega=vega,
            rho=rho,
            delta_explanation=delta_explanation,
            gamma_explanation=gamma_explanation,
            theta_explanation=theta_explanation,
            vega_explanation=vega_explanation,
            rho_explanation=rho_explanation
        )
    
    def _assess_options_risks(self, 
                            symbol: str,
                            option_type: str,
                            strike: float,
                            expiration: datetime,
                            stock_price: float,
                            iv: float,
                            volume: int,
                            open_interest: int) -> OptionsRiskAssessment:
        """Assess options-specific risks with educational warnings"""
        
        warnings = []
        educational_tips = []
        
        # Days to expiration
        days_to_exp = (expiration - datetime.now()).days
        
        # IV assessment (simplified - would need historical IV data)
        iv_rank = 50.0  # Placeholder - would calculate from historical data
        iv_percentile = 50.0  # Placeholder
        
        # Theta decay assessment
        theta_decay_daily = abs(iv * stock_price * 0.01)  # Simplified calculation
        
        if days_to_exp <= 7:
            warnings.append("⚠️ RAPID TIME DECAY: Less than 7 days to expiration")
            educational_tips.append("Options lose value quickly in final week - time decay accelerates")
            theta_decay_daily *= 2  # Accelerated decay
        elif days_to_exp <= 30:
            warnings.append("⏰ Moderate time decay: Less than 30 days to expiration")
            educational_tips.append("Time decay becomes more noticeable under 30 days")
        
        # Assignment risk assessment
        assignment_risk = "LOW"
        if option_type.lower() == "call" and stock_price > strike * 1.05:
            assignment_risk = "HIGH"
            warnings.append("🚨 HIGH ASSIGNMENT RISK: Deep in-the-money call")
            educational_tips.append("Deep ITM options may be assigned early, especially before dividends")
        elif option_type.lower() == "put" and stock_price < strike * 0.95:
            assignment_risk = "HIGH"
            warnings.append("🚨 HIGH ASSIGNMENT RISK: Deep in-the-money put")
            educational_tips.append("Deep ITM puts may be assigned early")
        elif abs(stock_price - strike) / stock_price < 0.02:  # Within 2%
            assignment_risk = "MODERATE"
            warnings.append("⚠️ Moderate assignment risk: Near the money")
        
        # Liquidity assessment
        liquidity_score = min(100, (volume + open_interest) / 100)
        
        if volume < 10:
            warnings.append("💧 LOW LIQUIDITY: Very low daily volume")
            educational_tips.append("Low volume options have wide bid-ask spreads - harder to trade")
        elif open_interest < 50:
            warnings.append("💧 LOW OPEN INTEREST: May be difficult to exit")
            educational_tips.append("Open interest shows how many contracts exist - higher is better for liquidity")
        
        # Earnings proximity (simplified - would need earnings calendar)
        earnings_proximity = 30  # Placeholder days
        
        if earnings_proximity <= 3:
            warnings.append("📊 EARNINGS RISK: Earnings within 3 days")
            educational_tips.append("IV often drops after earnings announcement - avoid buying options before earnings")
        elif earnings_proximity <= 7:
            warnings.append("📊 Earnings approaching: Within 1 week")
            educational_tips.append("Volatility may increase before earnings")
        
        # IV crush risk
        if iv > 0.5:  # 50% IV
            warnings.append("💥 IV CRUSH RISK: High implied volatility")
            educational_tips.append("High IV options are expensive - volatility may drop suddenly")
        
        return OptionsRiskAssessment(
            iv_rank=iv_rank,
            iv_percentile=iv_percentile,
            theta_decay_daily=theta_decay_daily,
            assignment_risk=assignment_risk,
            liquidity_score=liquidity_score,
            earnings_proximity=earnings_proximity,
            warnings=warnings,
            educational_tips=educational_tips
        )
    
    def _generate_beginner_summary(self, 
                                 symbol: str,
                                 option_type: str,
                                 strike: float,
                                 stock_price: float,
                                 greeks: OptionsGreeks,
                                 risk_assessment: OptionsRiskAssessment) -> str:
        """Generate beginner-friendly summary"""
        
        # Determine if option is ITM, ATM, or OTM
        if option_type.lower() == "call":
            if stock_price > strike:
                moneyness = f"IN-THE-MONEY (stock ${stock_price:.2f} > strike ${strike:.2f})"
            elif abs(stock_price - strike) / stock_price < 0.02:
                moneyness = f"AT-THE-MONEY (stock ≈ strike)"
            else:
                moneyness = f"OUT-OF-THE-MONEY (stock ${stock_price:.2f} < strike ${strike:.2f})"
        else:  # put
            if stock_price < strike:
                moneyness = f"IN-THE-MONEY (stock ${stock_price:.2f} < strike ${strike:.2f})"
            elif abs(stock_price - strike) / stock_price < 0.02:
                moneyness = f"AT-THE-MONEY (stock ≈ strike)"
            else:
                moneyness = f"OUT-OF-THE-MONEY (stock ${stock_price:.2f} > strike ${strike:.2f})"
        
        summary = f"""
🎓 BEGINNER'S GUIDE TO THIS {option_type.upper()} OPTION

📍 POSITION STATUS: {moneyness}

🔢 KEY NUMBERS TO UNDERSTAND:
• Delta: {greeks.delta:.2f} - {greeks.delta_explanation}
• Theta: ${greeks.theta:.2f}/day - {greeks.theta_explanation}

⚠️ MAIN RISKS:
{chr(10).join(['• ' + warning for warning in risk_assessment.warnings[:3]])}

💡 WHAT THIS MEANS:
This {option_type} option gives you the right to {'buy' if option_type.lower() == 'call' else 'sell'} 
{symbol} at ${strike:.2f}. Since the stock is currently at ${stock_price:.2f}, this option is {moneyness.split('(')[0].strip().lower()}.

🕐 TIME IS YOUR ENEMY: You're losing ${abs(greeks.theta):.2f} per day just from time passing.

🎯 FOR SUCCESS: You need {symbol} to move {'above' if option_type.lower() == 'call' else 'below'} 
${strike:.2f} by expiration, and move enough to overcome time decay.
        """.strip()
        
        return summary
    
    def _generate_advanced_insights(self, 
                                  option_type: str,
                                  strike: float,
                                  stock_price: float,
                                  greeks: OptionsGreeks,
                                  risk_assessment: OptionsRiskAssessment) -> List[str]:
        """Generate advanced trading insights"""
        
        insights = []
        
        # Delta insights
        if abs(greeks.delta) > 0.7:
            insights.append(f"High delta ({greeks.delta:.2f}) means this option moves almost like stock - consider if stock is better")
        elif abs(greeks.delta) < 0.3:
            insights.append(f"Low delta ({greeks.delta:.2f}) means large stock moves needed for profit")
        
        # Gamma insights
        if greeks.gamma > 0.1:
            insights.append(f"High gamma ({greeks.gamma:.3f}) means delta will change rapidly - position can accelerate quickly")
        
        # Theta insights
        if abs(greeks.theta) > stock_price * 0.01:
            insights.append(f"High theta decay (${abs(greeks.theta):.2f}/day) - time is working against you")
        
        # Vega insights
        if abs(greeks.vega) > 0.5:
            insights.append(f"High vega (${abs(greeks.vega):.2f}) - very sensitive to volatility changes")
        
        # Risk-specific insights
        if risk_assessment.assignment_risk == "HIGH":
            insights.append("Consider closing position before assignment to avoid stock ownership")
        
        if risk_assessment.liquidity_score < 50:
            insights.append("Poor liquidity - use limit orders and expect wider spreads")
        
        return insights
