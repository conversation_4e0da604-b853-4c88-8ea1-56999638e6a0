import"../shell/shell.js";import*as e from"../../core/i18n/i18n.js";import*as t from"../../core/root/root.js";import*as o from"../../core/sdk/sdk.js";import*as i from"../../ui/legacy/legacy.js";import*as n from"../../core/common/common.js";import*as r from"../../models/issues_manager/issues_manager.js";import*as a from"../../models/extensions/extensions.js";import*as s from"../../models/workspace/workspace.js";import*as l from"../../panels/timeline/utils/utils.js";import*as c from"../../panels/network/forward/forward.js";import*as g from"../../panels/application/preloading/helper/helper.js";import*as d from"../../panels/mobile_throttling/mobile_throttling.js";import*as w from"../../ui/legacy/components/utils/utils.js";import*as p from"../main/main.js";const m={showEventListenerBreakpoints:"Show Event Listener Breakpoints",eventListenerBreakpoints:"Event Listener Breakpoints",showCspViolationBreakpoints:"Show CSP Violation Breakpoints",cspViolationBreakpoints:"CSP Violation Breakpoints",showXhrfetchBreakpoints:"Show XHR/fetch Breakpoints",xhrfetchBreakpoints:"XHR/fetch Breakpoints",showDomBreakpoints:"Show DOM Breakpoints",domBreakpoints:"DOM Breakpoints",showGlobalListeners:"Show Global Listeners",globalListeners:"Global Listeners",page:"Page",showPage:"Show Page",overrides:"Overrides",showOverrides:"Show Overrides",contentScripts:"Content scripts",showContentScripts:"Show Content scripts",refreshGlobalListeners:"Refresh global listeners"},u=e.i18n.registerUIStrings("panels/browser_debugger/browser_debugger-meta.ts",m),R=e.i18n.getLazilyComputedLocalizedString.bind(void 0,u);let k,y;async function h(){return k||(k=await import("../../panels/browser_debugger/browser_debugger.js")),k}async function v(){return y||(y=await import("../../panels/sources/sources.js")),y}i.ViewManager.registerViewExtension({loadView:async()=>(await h()).EventListenerBreakpointsSidebarPane.EventListenerBreakpointsSidebarPane.instance(),id:"sources.event-listener-breakpoints",location:"sources.sidebar-bottom",commandPrompt:R(m.showEventListenerBreakpoints),title:R(m.eventListenerBreakpoints),order:9,persistence:"permanent"}),i.ViewManager.registerViewExtension({loadView:async()=>new((await h()).CSPViolationBreakpointsSidebarPane.CSPViolationBreakpointsSidebarPane),id:"sources.csp-violation-breakpoints",location:"sources.sidebar-bottom",commandPrompt:R(m.showCspViolationBreakpoints),title:R(m.cspViolationBreakpoints),order:10,persistence:"permanent"}),i.ViewManager.registerViewExtension({loadView:async()=>(await h()).XHRBreakpointsSidebarPane.XHRBreakpointsSidebarPane.instance(),id:"sources.xhr-breakpoints",location:"sources.sidebar-bottom",commandPrompt:R(m.showXhrfetchBreakpoints),title:R(m.xhrfetchBreakpoints),order:5,persistence:"permanent",hasToolbar:!0}),i.ViewManager.registerViewExtension({loadView:async()=>(await h()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance(),id:"sources.dom-breakpoints",location:"sources.sidebar-bottom",commandPrompt:R(m.showDomBreakpoints),title:R(m.domBreakpoints),order:7,persistence:"permanent"}),i.ViewManager.registerViewExtension({loadView:async()=>new((await h()).ObjectEventListenersSidebarPane.ObjectEventListenersSidebarPane),id:"sources.global-listeners",location:"sources.sidebar-bottom",commandPrompt:R(m.showGlobalListeners),title:R(m.globalListeners),order:8,persistence:"permanent",hasToolbar:!0}),i.ViewManager.registerViewExtension({loadView:async()=>(await h()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance(),id:"elements.dom-breakpoints",location:"elements-sidebar",commandPrompt:R(m.showDomBreakpoints),title:R(m.domBreakpoints),order:6,persistence:"permanent"}),i.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-network",title:R(m.page),commandPrompt:R(m.showPage),order:2,persistence:"permanent",loadView:async()=>(await v()).SourcesNavigator.NetworkNavigatorView.instance()}),i.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-overrides",title:R(m.overrides),commandPrompt:R(m.showOverrides),order:4,persistence:"permanent",loadView:async()=>(await v()).SourcesNavigator.OverridesNavigatorView.instance()}),i.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-content-scripts",title:R(m.contentScripts),commandPrompt:R(m.showContentScripts),order:5,persistence:"permanent",condition:()=>"/bundled/worker_app.html"!==t.Runtime.getPathName(),loadView:async()=>new((await v()).SourcesNavigator.ContentScriptsNavigatorView)}),i.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"browser-debugger.refresh-global-event-listeners",loadActionDelegate:async()=>new((await h()).ObjectEventListenersSidebarPane.ActionDelegate),title:R(m.refreshGlobalListeners),iconClass:"refresh",contextTypes:()=>void 0===k?[]:(e=>[e.ObjectEventListenersSidebarPane.ObjectEventListenersSidebarPane])(k)}),i.ContextMenu.registerProvider({contextTypes:()=>[o.DOMModel.DOMNode],loadProvider:async()=>new((await h()).DOMBreakpointsSidebarPane.ContextMenuProvider),experiment:void 0}),i.Context.registerListener({contextTypes:()=>[o.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await h()).XHRBreakpointsSidebarPane.XHRBreakpointsSidebarPane.instance()}),i.Context.registerListener({contextTypes:()=>[o.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await h()).DOMBreakpointsSidebarPane.DOMBreakpointsSidebarPane.instance()});const P={developerResources:"Developer resources",showDeveloperResources:"Show Developer resources"},b=e.i18n.registerUIStrings("panels/developer_resources/developer_resources-meta.ts",P),A=e.i18n.getLazilyComputedLocalizedString.bind(void 0,b);let E;async function T(){return E||(E=await import("../../panels/developer_resources/developer_resources.js")),E}i.ViewManager.registerViewExtension({location:"drawer-view",id:"developer-resources",title:A(P.developerResources),commandPrompt:A(P.showDeveloperResources),order:100,persistence:"closeable",loadView:async()=>new((await T()).DeveloperResourcesView.DeveloperResourcesView)}),n.Revealer.registerRevealer({contextTypes:()=>[o.PageResourceLoader.ResourceKey],destination:n.Revealer.RevealerDestination.DEVELOPER_RESOURCES_PANEL,loadRevealer:async()=>new((await T()).DeveloperResourcesView.DeveloperResourcesRevealer)});const S={issues:"Issues",showIssues:"Show Issues"},f=e.i18n.registerUIStrings("panels/issues/issues-meta.ts",S),N=e.i18n.getLazilyComputedLocalizedString.bind(void 0,f);let x;async function L(){return x||(x=await import("../../panels/issues/issues.js")),x}i.ViewManager.registerViewExtension({location:"drawer-view",id:"issues-pane",title:N(S.issues),commandPrompt:N(S.showIssues),order:100,persistence:"closeable",loadView:async()=>new((await L()).IssuesPane.IssuesPane)}),n.Revealer.registerRevealer({contextTypes:()=>[r.Issue.Issue],destination:n.Revealer.RevealerDestination.ISSUES_VIEW,loadRevealer:async()=>new((await L()).IssueRevealer.IssueRevealer)});const D={resetView:"Reset view",switchToPanMode:"Switch to pan mode",switchToRotateMode:"Switch to rotate mode",zoomIn:"Zoom in",zoomOut:"Zoom out",panOrRotateUp:"Pan or rotate up",panOrRotateDown:"Pan or rotate down",panOrRotateLeft:"Pan or rotate left",panOrRotateRight:"Pan or rotate right"},C=e.i18n.registerUIStrings("panels/layer_viewer/layer_viewer-meta.ts",D),I=e.i18n.getLazilyComputedLocalizedString.bind(void 0,C);i.ActionRegistration.registerActionExtension({actionId:"layers.reset-view",category:"LAYERS",title:I(D.resetView),bindings:[{shortcut:"0"}]}),i.ActionRegistration.registerActionExtension({actionId:"layers.pan-mode",category:"LAYERS",title:I(D.switchToPanMode),bindings:[{shortcut:"x"}]}),i.ActionRegistration.registerActionExtension({actionId:"layers.rotate-mode",category:"LAYERS",title:I(D.switchToRotateMode),bindings:[{shortcut:"v"}]}),i.ActionRegistration.registerActionExtension({actionId:"layers.zoom-in",category:"LAYERS",title:I(D.zoomIn),bindings:[{shortcut:"Shift+Plus"},{shortcut:"NumpadPlus"}]}),i.ActionRegistration.registerActionExtension({actionId:"layers.zoom-out",category:"LAYERS",title:I(D.zoomOut),bindings:[{shortcut:"Shift+Minus"},{shortcut:"NumpadMinus"}]}),i.ActionRegistration.registerActionExtension({actionId:"layers.up",category:"LAYERS",title:I(D.panOrRotateUp),bindings:[{shortcut:"Up"},{shortcut:"w"}]}),i.ActionRegistration.registerActionExtension({actionId:"layers.down",category:"LAYERS",title:I(D.panOrRotateDown),bindings:[{shortcut:"Down"},{shortcut:"s"}]}),i.ActionRegistration.registerActionExtension({actionId:"layers.left",category:"LAYERS",title:I(D.panOrRotateLeft),bindings:[{shortcut:"Left"},{shortcut:"a"}]}),i.ActionRegistration.registerActionExtension({actionId:"layers.right",category:"LAYERS",title:I(D.panOrRotateRight),bindings:[{shortcut:"Right"},{shortcut:"d"}]});const V={throttling:"Throttling",showThrottling:"Show Throttling",goOffline:"Go offline",device:"device",throttlingTag:"throttling",enableSlowGThrottling:"Enable slow `3G` throttling",enableFastGThrottling:"Enable fast `3G` throttling",goOnline:"Go online"},M=e.i18n.registerUIStrings("panels/mobile_throttling/mobile_throttling-meta.ts",V),O=e.i18n.getLazilyComputedLocalizedString.bind(void 0,M);let B;async function F(){return B||(B=await import("../../panels/mobile_throttling/mobile_throttling.js")),B}i.ViewManager.registerViewExtension({location:"settings-view",id:"throttling-conditions",title:O(V.throttling),commandPrompt:O(V.showThrottling),order:35,loadView:async()=>new((await F()).ThrottlingSettingsTab.ThrottlingSettingsTab),settings:["custom-network-conditions"],iconName:"performance"}),i.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-offline",category:"NETWORK",title:O(V.goOffline),loadActionDelegate:async()=>new((await F()).ThrottlingManager.ActionDelegate),tags:[O(V.device),O(V.throttlingTag)]}),i.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-low-end-mobile",category:"NETWORK",title:O(V.enableSlowGThrottling),loadActionDelegate:async()=>new((await F()).ThrottlingManager.ActionDelegate),tags:[O(V.device),O(V.throttlingTag)]}),i.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-mid-tier-mobile",category:"NETWORK",title:O(V.enableFastGThrottling),loadActionDelegate:async()=>new((await F()).ThrottlingManager.ActionDelegate),tags:[O(V.device),O(V.throttlingTag)]}),i.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-online",category:"NETWORK",title:O(V.goOnline),loadActionDelegate:async()=>new((await F()).ThrottlingManager.ActionDelegate),tags:[O(V.device),O(V.throttlingTag)]}),n.Settings.registerSettingExtension({storageType:"Synced",settingName:"custom-network-conditions",settingType:"array",defaultValue:[]});const U={showNetwork:"Show Network",network:"Network",networkExpoUnstable:"Network (Expo, unstable)",showNetworkRequestBlocking:"Show Network request blocking",networkRequestBlocking:"Network request blocking",showNetworkConditions:"Show Network conditions",networkConditions:"Network conditions",diskCache:"disk cache",networkThrottling:"network throttling",showSearch:"Show Search",search:"Search",recordNetworkLog:"Record network log",stopRecordingNetworkLog:"Stop recording network log",hideRequestDetails:"Hide request details",colorcodeResourceTypes:"Color-code resource types",colorCode:"color code",resourceType:"resource type",colorCodeByResourceType:"Color code by resource type",useDefaultColors:"Use default colors",groupNetworkLogByFrame:"Group network log by frame",netWork:"network",frame:"frame",group:"group",groupNetworkLogItemsByFrame:"Group network log items by frame",dontGroupNetworkLogItemsByFrame:"Don't group network log items by frame",clear:"Clear network log",addNetworkRequestBlockingPattern:"Add network request blocking pattern",removeAllNetworkRequestBlockingPatterns:"Remove all network request blocking patterns"},_=e.i18n.registerUIStrings("panels/network/network-meta.ts",U),q=e.i18n.getLazilyComputedLocalizedString.bind(void 0,_),j=e.i18n.getLocalizedString.bind(void 0,_);let W;async function z(){return W||(W=await import("../../panels/network/network.js")),W}function G(e){return void 0===W?[]:e(W)}i.ViewManager.registerViewExtension({location:"panel",id:"network",commandPrompt:q(U.showNetwork),title:()=>t.Runtime.experiments.isEnabled(t.Runtime.RNExperimentName.ENABLE_NETWORK_PANEL)?j(U.network):j(U.networkExpoUnstable),order:40,isPreviewFeature:!0,condition:t.Runtime.conditions.reactNativeUnstableNetworkPanel,loadView:async()=>(await z()).NetworkPanel.NetworkPanel.instance()}),i.ViewManager.registerViewExtension({location:"drawer-view",id:"network.blocked-urls",commandPrompt:q(U.showNetworkRequestBlocking),title:q(U.networkRequestBlocking),persistence:"closeable",order:60,loadView:async()=>new((await z()).BlockedURLsPane.BlockedURLsPane)}),i.ViewManager.registerViewExtension({location:"drawer-view",id:"network.config",commandPrompt:q(U.showNetworkConditions),title:q(U.networkConditions),persistence:"closeable",order:40,tags:[q(U.diskCache),q(U.networkThrottling),e.i18n.lockedLazyString("useragent"),e.i18n.lockedLazyString("user agent"),e.i18n.lockedLazyString("user-agent")],loadView:async()=>(await z()).NetworkConfigView.NetworkConfigView.instance()}),i.ViewManager.registerViewExtension({location:"network-sidebar",id:"network.search-network-tab",commandPrompt:q(U.showSearch),title:q(U.search),persistence:"permanent",loadView:async()=>(await z()).NetworkPanel.SearchNetworkView.instance()}),i.ActionRegistration.registerActionExtension({actionId:"network.toggle-recording",category:"NETWORK",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>G((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await z()).NetworkPanel.ActionDelegate),options:[{value:!0,title:q(U.recordNetworkLog)},{value:!1,title:q(U.stopRecordingNetworkLog)}],bindings:[{shortcut:"Ctrl+E",platform:"windows,linux"},{shortcut:"Meta+E",platform:"mac"}]}),i.ActionRegistration.registerActionExtension({actionId:"network.clear",category:"NETWORK",title:q(U.clear),iconClass:"clear",loadActionDelegate:async()=>new((await z()).NetworkPanel.ActionDelegate),contextTypes:()=>G((e=>[e.NetworkPanel.NetworkPanel])),bindings:[{shortcut:"Ctrl+L"},{shortcut:"Meta+K",platform:"mac"}]}),i.ActionRegistration.registerActionExtension({actionId:"network.hide-request-details",category:"NETWORK",title:q(U.hideRequestDetails),contextTypes:()=>G((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await z()).NetworkPanel.ActionDelegate),bindings:[{shortcut:"Esc"}]}),i.ActionRegistration.registerActionExtension({actionId:"network.search",category:"NETWORK",title:q(U.search),contextTypes:()=>G((e=>[e.NetworkPanel.NetworkPanel])),loadActionDelegate:async()=>new((await z()).NetworkPanel.ActionDelegate),bindings:[{platform:"mac",shortcut:"Meta+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+F",keybindSets:["devToolsDefault","vsCode"]}]}),i.ActionRegistration.registerActionExtension({actionId:"network.add-network-request-blocking-pattern",category:"NETWORK",title:q(U.addNetworkRequestBlockingPattern),iconClass:"plus",contextTypes:()=>G((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await z()).BlockedURLsPane.ActionDelegate)}),i.ActionRegistration.registerActionExtension({actionId:"network.remove-all-network-request-blocking-patterns",category:"NETWORK",title:q(U.removeAllNetworkRequestBlockingPatterns),iconClass:"clear",contextTypes:()=>G((e=>[e.BlockedURLsPane.BlockedURLsPane])),loadActionDelegate:async()=>new((await z()).BlockedURLsPane.ActionDelegate)}),n.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:q(U.colorcodeResourceTypes),settingName:"network-color-code-resource-types",settingType:"boolean",defaultValue:!1,tags:[q(U.colorCode),q(U.resourceType)],options:[{value:!0,title:q(U.colorCodeByResourceType)},{value:!1,title:q(U.useDefaultColors)}]}),n.Settings.registerSettingExtension({category:"NETWORK",storageType:"Synced",title:q(U.groupNetworkLogByFrame),settingName:"network.group-by-frame",settingType:"boolean",defaultValue:!1,tags:[q(U.netWork),q(U.frame),q(U.group)],options:[{value:!0,title:q(U.groupNetworkLogItemsByFrame)},{value:!1,title:q(U.dontGroupNetworkLogItemsByFrame)}]}),i.ViewManager.registerLocationResolver({name:"network-sidebar",category:"NETWORK",loadResolver:async()=>(await z()).NetworkPanel.NetworkPanel.instance()}),i.ContextMenu.registerProvider({contextTypes:()=>[o.NetworkRequest.NetworkRequest,o.Resource.Resource,s.UISourceCode.UISourceCode,l.NetworkRequest.TimelineNetworkRequest],loadProvider:async()=>(await z()).NetworkPanel.NetworkPanel.instance(),experiment:void 0}),n.Revealer.registerRevealer({contextTypes:()=>[o.NetworkRequest.NetworkRequest],destination:n.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await z()).NetworkPanel.RequestRevealer)}),n.Revealer.registerRevealer({contextTypes:()=>[c.UIRequestLocation.UIRequestLocation],destination:void 0,loadRevealer:async()=>new((await z()).NetworkPanel.RequestLocationRevealer)}),n.Revealer.registerRevealer({contextTypes:()=>[c.NetworkRequestId.NetworkRequestId],destination:n.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await z()).NetworkPanel.RequestIdRevealer)}),n.Revealer.registerRevealer({contextTypes:()=>[c.UIFilter.UIRequestFilter,a.ExtensionServer.RevealableNetworkRequestFilter],destination:n.Revealer.RevealerDestination.NETWORK_PANEL,loadRevealer:async()=>new((await z()).NetworkPanel.NetworkLogWithFilterRevealer)});const K={application:"Application",showApplication:"Show Application",pwa:"pwa",clearSiteData:"Clear site data",clearSiteDataIncludingThirdparty:"Clear site data (including third-party cookies)",startRecordingEvents:"Start recording events",stopRecordingEvents:"Stop recording events"},Y=e.i18n.registerUIStrings("panels/application/application-meta.ts",K),H=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Y);let X;async function Z(){return X||(X=await import("../../panels/application/application.js")),X}i.ViewManager.registerViewExtension({location:"panel",id:"resources",title:H(K.application),commandPrompt:H(K.showApplication),order:70,loadView:async()=>(await Z()).ResourcesPanel.ResourcesPanel.instance(),tags:[H(K.pwa)]}),i.ActionRegistration.registerActionExtension({category:"RESOURCES",actionId:"resources.clear",title:H(K.clearSiteData),loadActionDelegate:async()=>new((await Z()).StorageView.ActionDelegate)}),i.ActionRegistration.registerActionExtension({category:"RESOURCES",actionId:"resources.clear-incl-third-party-cookies",title:H(K.clearSiteDataIncludingThirdparty),loadActionDelegate:async()=>new((await Z()).StorageView.ActionDelegate)}),i.ActionRegistration.registerActionExtension({actionId:"background-service.toggle-recording",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>void 0===X?[]:(e=>[e.BackgroundServiceView.BackgroundServiceView])(X),loadActionDelegate:async()=>new((await Z()).BackgroundServiceView.ActionDelegate),category:"BACKGROUND_SERVICES",options:[{value:!0,title:H(K.startRecordingEvents)},{value:!1,title:H(K.stopRecordingEvents)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),n.Revealer.registerRevealer({contextTypes:()=>[o.Resource.Resource],destination:n.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await Z()).ResourcesPanel.ResourceRevealer)}),n.Revealer.registerRevealer({contextTypes:()=>[o.ResourceTreeModel.ResourceTreeFrame],destination:n.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await Z()).ResourcesPanel.FrameDetailsRevealer)}),n.Revealer.registerRevealer({contextTypes:()=>[g.PreloadingForward.RuleSetView],destination:n.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await Z()).ResourcesPanel.RuleSetViewRevealer)}),n.Revealer.registerRevealer({contextTypes:()=>[g.PreloadingForward.AttemptViewWithFilter],destination:n.Revealer.RevealerDestination.APPLICATION_PANEL,loadRevealer:async()=>new((await Z()).ResourcesPanel.AttemptViewWithFilterRevealer)});const J={performance:"Performance",showPerformance:"Show Performance",record:"Record",stop:"Stop",recordAndReload:"Record and reload",saveProfile:"Save profile…",loadProfile:"Load profile…",previousFrame:"Previous frame",nextFrame:"Next frame",showRecentTimelineSessions:"Show recent timeline sessions",previousRecording:"Previous recording",nextRecording:"Next recording",hideChromeFrameInLayersView:"Hide `chrome` frame in Layers view"},Q=e.i18n.registerUIStrings("panels/timeline/timeline-meta.ts",J),$=e.i18n.getLazilyComputedLocalizedString.bind(void 0,Q);let ee;async function te(){return ee||(ee=await import("../../panels/timeline/timeline.js")),ee}function oe(e){return void 0===ee?[]:e(ee)}i.ViewManager.registerViewExtension({location:"panel",id:"timeline",title:$(J.performance),commandPrompt:$(J.showPerformance),order:50,experiment:!0===globalThis.FB_ONLY__enablePerformance?void 0:"enable-performance-panel",loadView:async()=>(await te()).TimelinePanel.TimelinePanel.instance()}),i.ActionRegistration.registerActionExtension({actionId:"timeline.toggle-recording",category:"PERFORMANCE",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>oe((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await te()).TimelinePanel.ActionDelegate),options:[{value:!0,title:$(J.record)},{value:!1,title:$(J.stop)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),i.ActionRegistration.registerActionExtension({actionId:"timeline.record-reload",iconClass:"refresh",contextTypes:()=>oe((e=>[e.TimelinePanel.TimelinePanel])),category:"PERFORMANCE",title:$(J.recordAndReload),loadActionDelegate:async()=>new((await te()).TimelinePanel.ActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}],experiment:"!react-native-specific-ui"}),i.ActionRegistration.registerActionExtension({category:"PERFORMANCE",actionId:"timeline.save-to-file",contextTypes:()=>oe((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await te()).TimelinePanel.ActionDelegate),title:$(J.saveProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+S"},{platform:"mac",shortcut:"Meta+S"}]}),i.ActionRegistration.registerActionExtension({category:"PERFORMANCE",actionId:"timeline.load-from-file",contextTypes:()=>oe((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await te()).TimelinePanel.ActionDelegate),title:$(J.loadProfile),bindings:[{platform:"windows,linux",shortcut:"Ctrl+O"},{platform:"mac",shortcut:"Meta+O"}]}),i.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-previous-frame",category:"PERFORMANCE",title:$(J.previousFrame),contextTypes:()=>oe((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await te()).TimelinePanel.ActionDelegate),bindings:[{shortcut:"["}]}),i.ActionRegistration.registerActionExtension({actionId:"timeline.jump-to-next-frame",category:"PERFORMANCE",title:$(J.nextFrame),contextTypes:()=>oe((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await te()).TimelinePanel.ActionDelegate),bindings:[{shortcut:"]"}]}),i.ActionRegistration.registerActionExtension({actionId:"timeline.show-history",loadActionDelegate:async()=>new((await te()).TimelinePanel.ActionDelegate),category:"PERFORMANCE",title:$(J.showRecentTimelineSessions),contextTypes:()=>oe((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+H"},{platform:"mac",shortcut:"Meta+Y"}]}),i.ActionRegistration.registerActionExtension({actionId:"timeline.previous-recording",category:"PERFORMANCE",loadActionDelegate:async()=>new((await te()).TimelinePanel.ActionDelegate),title:$(J.previousRecording),contextTypes:()=>oe((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Left"},{platform:"mac",shortcut:"Meta+Left"}]}),i.ActionRegistration.registerActionExtension({actionId:"timeline.next-recording",category:"PERFORMANCE",loadActionDelegate:async()=>new((await te()).TimelinePanel.ActionDelegate),title:$(J.nextRecording),contextTypes:()=>oe((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Alt+Right"},{platform:"mac",shortcut:"Meta+Right"}]}),n.Settings.registerSettingExtension({category:"PERFORMANCE",storageType:"Synced",title:$(J.hideChromeFrameInLayersView),settingName:"frame-viewer-hide-chrome-window",settingType:"boolean",defaultValue:!1}),n.Linkifier.registerLinkifier({contextTypes:()=>oe((e=>[e.CLSLinkifier.CLSRect])),loadLinkifier:async()=>(await te()).CLSLinkifier.Linkifier.instance()}),i.ContextMenu.registerItem({location:"timelineMenu/open",actionId:"timeline.load-from-file",order:10}),i.ContextMenu.registerItem({location:"timelineMenu/open",actionId:"timeline.save-to-file",order:15});const ie={main:"Main"},ne=e.i18n.registerUIStrings("entrypoints/worker_app/WorkerMain.ts",ie),re=e.i18n.getLocalizedString.bind(void 0,ne);let ae;class se{static instance(e={forceNew:null}){const{forceNew:t}=e;return ae&&!t||(ae=new se),ae}async run(){o.Connections.initMainConnection((async()=>{await o.TargetManager.TargetManager.instance().maybeAttachInitialTarget()||o.TargetManager.TargetManager.instance().createTarget("main",re(ie.main),o.Target.Type.ServiceWorker,null)}),w.TargetDetachedDialog.TargetDetachedDialog.webSocketConnectionLost),new d.NetworkPanelIndicator.NetworkPanelIndicator}}n.Runnable.registerEarlyInitializationRunnable(se.instance),o.ChildTargetManager.ChildTargetManager.install((async({target:e,waitingForDebugger:t})=>{if(e.parentTarget()||e.type()!==o.Target.Type.ServiceWorker||!t)return;const i=e.model(o.DebuggerModel.DebuggerModel);i&&(i.isReadyToPause()||await i.once(o.DebuggerModel.Events.DebuggerIsReadyToPause),i.pause())})),self.runtime=t.Runtime.Runtime.instance({forceNew:!0}),new p.MainImpl.MainImpl;
