/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 * @oncall react_native
 */

import type { InspectorProxyQueries } from "../inspector-proxy/InspectorProxy";
import type { BrowserLauncher } from "../types/BrowserLauncher";
import type { EventReporter } from "../types/EventReporter";
import type { Experiments } from "../types/Experiments";
import type { Logger } from "../types/Logger";
import type { NextHandleFunction } from "connect";
type Options = $ReadOnly<{
  serverBaseUrl: string,
  logger?: Logger,
  browserLauncher: <PERSON>rowserLauncher,
  eventReporter?: EventReporter,
  experiments: Experiments,
  inspectorProxy: InspectorProxyQueries,
}>;

/**
 * Open the debugger frontend for a given CDP target.
 *
 * Currently supports React Native DevTools (rn_fusebox.html) and legacy Hermes
 * (rn_inspector.html) targets.
 *
 * @see https://chromedevtools.github.io/devtools-protocol/
 */
declare export default function openDebuggerMiddleware(
  $$PARAM_0$$: Options
): NextHandleFunction;
