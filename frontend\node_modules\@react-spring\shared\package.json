{"name": "@react-spring/shared", "version": "9.7.5", "description": "Globals and shared modules", "module": "./dist/react-spring_shared.legacy-esm.js", "main": "./dist/cjs/index.js", "types": "./dist/react-spring_shared.modern.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/react-spring_shared.modern.d.ts", "import": "./dist/react-spring_shared.modern.mjs", "require": "./dist/cjs/index.js"}}, "files": ["dist/**/*", "README.md", "LICENSE"], "repository": "pmndrs/react-spring", "homepage": "https://github.com/pmndrs/react-spring#readme", "keywords": ["animated", "animation", "hooks", "motion", "react", "react-native", "spring", "typescript", "velocity"], "license": "MIT", "author": "<PERSON>", "maintainers": ["<PERSON> (https://github.com/joshua<PERSON>s)"], "dependencies": {"@react-spring/rafz": "~9.7.5", "@react-spring/types": "~9.7.5"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "scripts": {"build": "tsup", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist", "dev": "tsup --watch", "lint": "TIMING=1 eslint \"src/**/*.ts*\"", "pack": "yarn pack"}}