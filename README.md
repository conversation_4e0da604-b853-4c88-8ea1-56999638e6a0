# 🧠 A.T.L.A.S AI Trading System with Chain-of-Thought Intelligence

## 🎯 Overview

A.T.L.A.S (Advanced Trading & Learning Analysis System) is a comprehensive AI-powered trading platform that combines sophisticated technical analysis with transparent, beginner-friendly explanations through Chain-of-Thought reasoning.

**Key Innovation**: Every trading decision includes step-by-step reasoning that beginners can understand, making professional-grade trading accessible through educational AI explanations.

## 🌟 Key Features

### **Chain-of-Thought Trading Intelligence**
- **Transparent Reasoning**: Step-by-step explanations for every trading decision
- **Beginner-Friendly**: Complex concepts explained with simple analogies
- **Educational Focus**: Learn while you trade with comprehensive explanations
- **Confidence Scoring**: Know exactly how confident the AI is in each recommendation

### **Advanced Trading Capabilities**
- **TTM Squeeze Pattern Detection**: Proven technical analysis with 65% historical win rate
- **Profit-Targeted Strategies**: Work backwards from your profit goals
- **Kelly Criterion Position Sizing**: Mathematical optimization for risk-adjusted returns
- **Real-Time Execution**: Automatic stop-losses and target orders
- **Options Education**: Greeks explanations and risk assessment

### **Comprehensive Safety Features**
- **Daily Loss Limits**: Maximum 3% daily loss with automatic suspension
- **Position Size Limits**: Maximum 20% per position for diversification
- **Volatility Circuit Breakers**: Trading paused during market chaos (VIX > 40)
- **Confidence Thresholds**: Only high-quality signals (70%+ confidence) executed
- **Paper Trading Mode**: Practice safely with virtual money

## 🏗️ Clean Architecture

```
A.T.L.A.S/
├── main.py                        # Main entry point
├── requirements.txt               # Python dependencies
├── docker-compose.yml            # Docker deployment
├── Dockerfile                    # Container configuration
│
├── streamlined/                   # Core A.T.L.A.S system
│   ├── atlas_server.py           # Main FastAPI server
│   ├── models.py                 # Data models and schemas
│   ├── config.py                 # Configuration and settings
│   ├── market_data.py            # Market data integration
│   ├── technical_analysis.py     # Technical analysis engine
│   ├── ai_services.py            # AI and LLM services
│   ├── trading_engine.py         # Trading execution
│   ├── trading_books_rag.py      # Educational RAG system
│   │
│   # Chain-of-Thought Components
│   ├── chain_of_thought_engine.py      # Core CoT analysis
│   ├── profit_strategy_engine.py       # Goal-oriented strategies
│   ├── risk_management_engine.py       # Enhanced risk management
│   ├── options_education_engine.py     # Options education
│   ├── execution_monitoring_engine.py  # Real-time execution
│   ├── safety_guardrails.py           # Comprehensive safety
│   ├── conversational_cot_interface.py # ChatGPT-style interface
│   ├── cot_trading_orchestrator.py    # Master coordinator
│   │
│   └── frontend/                 # Web interface
│       ├── index.js             # React frontend
│       └── atlas_app.js         # Main app component
│
└── frontend/                     # Alternative frontend build
    ├── package.json             # Node.js dependencies
    └── src/                     # React source files
```

## 🚀 Quick Start

### **1. Installation**
```bash
# Install Python dependencies
pip install -r requirements.txt

# Set up environment variables (create .env file)
OPENAI_API_KEY=your-openai-key
APCA_API_KEY_ID=your-alpaca-key
APCA_API_SECRET_KEY=your-alpaca-secret
FMP_API_KEY=your-fmp-key
```

### **2. Start the System**
```bash
# Option 1: Use main entry point
python main.py

# Option 2: Start directly from streamlined
cd streamlined
python atlas_server.py
```

### **3. Access the Interface**
- **Web Interface**: http://localhost:8080
- **API Documentation**: http://localhost:8080/docs
- **Health Check**: http://localhost:8080/api/v1/health

## 🎯 Usage Examples

### **Create a Trading Plan**
```bash
curl -X POST "http://localhost:8080/api/v1/cot/create-plan" \
  -H "Content-Type: application/json" \
  -d '{
    "user_request": "Make me $300 today",
    "account_size": 50000,
    "risk_tolerance": "moderate"
  }'
```

### **Analyze a Stock with Chain-of-Thought**
```bash
curl -X POST "http://localhost:8080/api/v1/cot/analyze-symbol" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "AAPL",
    "account_size": 50000
  }'
```

### **Conversational Interface**
```bash
curl -X POST "http://localhost:8080/api/v1/chat/cot" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Analyze AAPL with chain of thought reasoning"
  }'
```

## 📚 Educational Philosophy

### **Transparency First**
Every decision includes detailed explanations:
- **Why this stock?** Technical analysis with analogies
- **How much to buy?** Mathematical position sizing
- **What could go wrong?** Risk assessment with warnings
- **When to exit?** Stop-loss and target explanations

### **Beginner-Friendly Analogies**
- **Bollinger Bands**: "Like a rubber band around price"
- **Momentum**: "Like a car accelerating uphill"
- **Volume**: "Like a busy marketplace"
- **Risk Management**: "Like wearing a seatbelt"

## 🛡️ Safety Features

### **Automatic Protections**
- **Daily Loss Limits**: 3% maximum daily loss
- **Position Limits**: 20% maximum single position
- **Correlation Limits**: 85% maximum between positions
- **Volatility Breaks**: Trading suspended when VIX > 40
- **Confidence Gates**: 70% minimum signal confidence

## 🔧 API Endpoints

### **Chain-of-Thought Endpoints**
- `POST /api/v1/cot/create-plan` - Create comprehensive trading plan
- `POST /api/v1/cot/analyze-symbol` - Full CoT analysis of any symbol
- `GET /api/v1/cot/dashboard` - Real-time portfolio dashboard
- `POST /api/v1/chat/cot` - Enhanced conversational interface

### **Traditional Endpoints**
- `POST /api/v1/scan/ttm-squeeze` - TTM Squeeze pattern scanning
- `POST /api/v1/chat` - Basic AI chat
- `POST /api/v1/education/query` - Trading education queries
- `GET /api/v1/health` - System health check

## 📊 Performance Metrics

### **TTM Squeeze Pattern**
- **Historical Win Rate**: 65%
- **Average Winning Trade**: 8%
- **Average Losing Trade**: 3%
- **Risk/Reward Ratio**: 2.67:1

### **Safety Record**
- **Maximum Daily Loss**: 3% (hard limit)
- **Position Size Limit**: 20% (hard limit)
- **Confidence Threshold**: 70% (minimum)
- **Paper Trading**: Required for new users

## 🎓 Learning Resources

- **Chain-of-Thought README**: `streamlined/CHAIN_OF_THOUGHT_README.md`
- **Deployment Guide**: `streamlined/DEPLOYMENT_GUIDE.md`
- **API Documentation**: Available at `/docs` when server is running
- **Educational Queries**: Use the `/api/v1/education/query` endpoint

## ⚠️ Important Disclaimers

- **No Guarantees**: No trading strategy is 100% accurate
- **Educational Purpose**: System designed for learning and education
- **Risk Warning**: Only trade with money you can afford to lose
- **Paper Trading**: Start with virtual money to practice safely

## 🚀 Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up --build

# Or build manually
docker build -t atlas-ai .
docker run -p 8080:8080 atlas-ai
```

---

**Remember**: The goal isn't just to make money, but to become a better, more educated trader. A.T.L.A.S is your patient, knowledgeable mentor that explains every step of the journey! 🎓📈
