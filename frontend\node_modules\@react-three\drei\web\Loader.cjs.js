"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("../core/Progress.cjs.js");function r(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}require("three"),require("zustand");var n=r(e);const a=e=>`Loading ${e.toFixed(2)}%`;const i={container:{position:"absolute",top:0,left:0,width:"100%",height:"100%",background:"#171717",display:"flex",alignItems:"center",justifyContent:"center",transition:"opacity 300ms ease",zIndex:1e3},inner:{width:100,height:3,background:"#272727",textAlign:"center"},bar:{height:3,width:"100%",background:"white",transition:"transform 200ms",transformOrigin:"left center"},data:{display:"inline-block",position:"relative",fontVariantNumeric:"tabular-nums",marginTop:"0.8em",color:"#f0f0f0",fontSize:"0.6em",fontFamily:'-apple-system, BlinkMacSystemFont, "Inter", "Segoe UI", "Helvetica Neue", Helvetica, Arial, Roboto, Ubuntu, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',whiteSpace:"nowrap"}};exports.Loader=function({containerStyles:e,innerStyles:r,barStyles:o,dataStyles:c,dataInterpolation:s=a,initialState:l=e=>e}){const{active:u,progress:f}=t.useProgress(),m=n.useRef(0),d=n.useRef(0),p=n.useRef(null),[y,b]=n.useState(l(u));n.useEffect((()=>{let e;return u!==y&&(e=setTimeout((()=>b(u)),300)),()=>clearTimeout(e)}),[y,u]);const g=n.useCallback((()=>{p.current&&(m.current+=(f-m.current)/2,(m.current>.95*f||100===f)&&(m.current=f),p.current.innerText=s(m.current),m.current<f&&(d.current=requestAnimationFrame(g)))}),[s,f]);return n.useEffect((()=>(g(),()=>cancelAnimationFrame(d.current))),[g]),y?n.createElement("div",{style:{...i.container,opacity:u?1:0,...e}},n.createElement("div",null,n.createElement("div",{style:{...i.inner,...r}},n.createElement("div",{style:{...i.bar,transform:`scaleX(${f/100})`,...o}}),n.createElement("span",{ref:p,style:{...i.data,...c}})))):null};
