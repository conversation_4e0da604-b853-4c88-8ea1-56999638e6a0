"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),t=require("@react-three/fiber");function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var r=n(e);exports.CycleRaycast=function({onChanged:e,portal:n,preventDefault:o=!0,scroll:l=!0,keyCode:u=9}){const c=r.useRef(0),d=t.useThree((e=>e.setEvents)),i=t.useThree((e=>e.get)),a=t.useThree((e=>e.gl));return r.useEffect((()=>{var t;let r,s=[];const v=i().events.filter,f=null!==(t=null==n?void 0:n.current)&&void 0!==t?t:a.domElement.parentNode,h=()=>f&&e&&e(s,Math.round(c.current)%s.length);d({filter:(e,t)=>{let n=[...e];n.length===s.length&&s.every((e=>n.map((e=>e.object.uuid)).includes(e.object.uuid)))||(c.current=0,s=n,h()),v&&(n=v(n,t));for(let e=0;e<Math.round(c.current)%n.length;e++){const e=n.shift();n=[...n,e]}return n}});const m=e=>{var t,n;c.current=e(c.current),null==(t=i().events.handlers)||t.onPointerCancel(void 0),null==(n=i().events.handlers)||n.onPointerMove(r),h()},p=e=>{(e.keyCode||e.which)===u&&(o&&e.preventDefault(),s.length>1&&m((e=>e+1)))},g=e=>{o&&e.preventDefault();let t=0;e||(e=window.event),e.wheelDelta?t=e.wheelDelta/120:e.detail&&(t=-e.detail/3),s.length>1&&m((e=>Math.abs(e-t)))},b=e=>r=e;return document.addEventListener("pointermove",b,{passive:!0}),l&&document.addEventListener("wheel",g),void 0!==u&&document.addEventListener("keydown",p),()=>{d({filter:v}),void 0!==u&&document.removeEventListener("keydown",p),l&&document.removeEventListener("wheel",g),document.removeEventListener("pointermove",b)}}),[a,i,d,o,l,u]),null};
