"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("react-dom/client"),n=require("three"),o=require("@react-three/fiber");function i(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function s(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var a=i(e),c=s(t),l=s(r);const u=new n.Vector3,d=new n.Vector3,m=new n.Vector3,f=new n.Vector2;function h(e,t,r){const n=u.setFromMatrixPosition(e.matrixWorld);n.project(t);const o=r.width/2,i=r.height/2;return[n.x*o+o,-n.y*i+i]}const p=e=>Math.abs(e)<1e-10?0:e;function x(e,t,r=""){let n="matrix3d(";for(let r=0;16!==r;r++)n+=p(t[r]*e.elements[r])+(15!==r?",":")");return r+n}const v=(y=[1,-1,1,1,1,-1,1,1,1,-1,1,1,1,-1,1,1],e=>x(e,y));var y;const g=(e,t)=>{return x(e,[1/(r=t),1/r,1/r,1,-1/r,-1/r,-1/r,-1,1/r,1/r,1/r,1,1,1,1,1],"translate(-50%,-50%)");var r};const M=c.forwardRef((({children:e,eps:t=.001,style:r,className:i,prepend:s,center:x,fullscreen:y,portal:M,distanceFactor:b,sprite:P=!1,transform:w=!1,occlude:E,onOcclude:W,castShadow:S,receiveShadow:$,material:j,geometry:z,zIndexRange:O=[16777271,0],calculatePosition:R=h,as:F="div",wrapperClass:C,pointerEvents:T="auto",...I},A)=>{const{gl:k,camera:H,scene:N,size:V,raycaster:_,events:q,viewport:L}=o.useThree(),[D]=c.useState((()=>document.createElement(F))),G=c.useRef(),Z=c.useRef(null),B=c.useRef(0),J=c.useRef([0,0]),K=c.useRef(null),Q=c.useRef(null),U=(null==M?void 0:M.current)||q.connected||k.domElement.parentNode,X=c.useRef(null),Y=c.useRef(!1),ee=c.useMemo((()=>E&&"blending"!==E||Array.isArray(E)&&E.length&&function(e){return e&&"object"==typeof e&&"current"in e}(E[0])),[E]);c.useLayoutEffect((()=>{const e=k.domElement;E&&"blending"===E?(e.style.zIndex=`${Math.floor(O[0]/2)}`,e.style.position="absolute",e.style.pointerEvents="none"):(e.style.zIndex=null,e.style.position=null,e.style.pointerEvents=null)}),[E]),c.useLayoutEffect((()=>{if(Z.current){const e=G.current=l.createRoot(D);if(N.updateMatrixWorld(),w)D.style.cssText="position:absolute;top:0;left:0;pointer-events:none;overflow:hidden;";else{const e=R(Z.current,H,V);D.style.cssText=`position:absolute;top:0;left:0;transform:translate3d(${e[0]}px,${e[1]}px,0);transform-origin:0 0;`}return U&&(s?U.prepend(D):U.appendChild(D)),()=>{U&&U.removeChild(D),e.unmount()}}}),[U,w]),c.useLayoutEffect((()=>{C&&(D.className=C)}),[C]);const te=c.useMemo((()=>w?{position:"absolute",top:0,left:0,width:V.width,height:V.height,transformStyle:"preserve-3d",pointerEvents:"none"}:{position:"absolute",transform:x?"translate3d(-50%,-50%,0)":"none",...y&&{top:-V.height/2,left:-V.width/2,width:V.width,height:V.height},...r}),[r,x,y,V,w]),re=c.useMemo((()=>({position:"absolute",pointerEvents:T})),[T]);c.useLayoutEffect((()=>{var t,n;(Y.current=!1,w)?null==(t=G.current)||t.render(c.createElement("div",{ref:K,style:te},c.createElement("div",{ref:Q,style:re},c.createElement("div",{ref:A,className:i,style:r,children:e})))):null==(n=G.current)||n.render(c.createElement("div",{ref:A,style:te,className:i,children:e}))}));const ne=c.useRef(!0);o.useFrame((e=>{if(Z.current){H.updateMatrixWorld(),Z.current.updateWorldMatrix(!0,!1);const e=w?J.current:R(Z.current,H,V);if(w||Math.abs(B.current-H.zoom)>t||Math.abs(J.current[0]-e[0])>t||Math.abs(J.current[1]-e[1])>t){const t=function(e,t){const r=u.setFromMatrixPosition(e.matrixWorld),n=d.setFromMatrixPosition(t.matrixWorld),o=r.sub(n),i=t.getWorldDirection(m);return o.angleTo(i)>Math.PI/2}(Z.current,H);let r=!1;ee&&(Array.isArray(E)?r=E.map((e=>e.current)):"blending"!==E&&(r=[N]));const o=ne.current;if(r){const e=function(e,t,r,n){const o=u.setFromMatrixPosition(e.matrixWorld),i=o.clone();i.project(t),f.set(i.x,i.y),r.setFromCamera(f,t);const s=r.intersectObjects(n,!0);if(s.length){const e=s[0].distance;return o.distanceTo(r.ray.origin)<e}return!0}(Z.current,H,_,r);ne.current=e&&!t}else ne.current=!t;o!==ne.current&&(W?W(!ne.current):D.style.display=ne.current?"block":"none");const i=Math.floor(O[0]/2),s=E?ee?[O[0],i]:[i-1,0]:O;if(D.style.zIndex=`${function(e,t,r){if(t instanceof n.PerspectiveCamera||t instanceof n.OrthographicCamera){const n=u.setFromMatrixPosition(e.matrixWorld),o=d.setFromMatrixPosition(t.matrixWorld),i=n.distanceTo(o),s=(r[1]-r[0])/(t.far-t.near),a=r[1]-s*t.far;return Math.round(s*i+a)}}(Z.current,H,s)}`,w){const[e,t]=[V.width/2,V.height/2],r=H.projectionMatrix.elements[5]*t,{isOrthographicCamera:n,top:o,left:i,bottom:s,right:a}=H,c=v(H.matrixWorldInverse),l=n?`scale(${r})translate(${p(-(a+i)/2)}px,${p((o+s)/2)}px)`:`translateZ(${r}px)`;let u=Z.current.matrixWorld;P&&(u=H.matrixWorldInverse.clone().transpose().copyPosition(u).scale(Z.current.scale),u.elements[3]=u.elements[7]=u.elements[11]=0,u.elements[15]=1),D.style.width=V.width+"px",D.style.height=V.height+"px",D.style.perspective=n?"":`${r}px`,K.current&&Q.current&&(K.current.style.transform=`${l}${c}translate(${e}px,${t}px)`,Q.current.style.transform=g(u,1/((b||10)/400)))}else{const t=void 0===b?1:function(e,t){if(t instanceof n.OrthographicCamera)return t.zoom;if(t instanceof n.PerspectiveCamera){const r=u.setFromMatrixPosition(e.matrixWorld),n=d.setFromMatrixPosition(t.matrixWorld),o=t.fov*Math.PI/180,i=r.distanceTo(n);return 1/(2*Math.tan(o/2)*i)}return 1}(Z.current,H)*b;D.style.transform=`translate3d(${e[0]}px,${e[1]}px,0) scale(${t})`}J.current=e,B.current=H.zoom}}if(!ee&&X.current&&!Y.current)if(w){if(K.current){const e=K.current.children[0];if(null!=e&&e.clientWidth&&null!=e&&e.clientHeight){const{isOrthographicCamera:t}=H;if(t||z)I.scale&&(Array.isArray(I.scale)?I.scale instanceof n.Vector3?X.current.scale.copy(I.scale.clone().divideScalar(1)):X.current.scale.set(1/I.scale[0],1/I.scale[1],1/I.scale[2]):X.current.scale.setScalar(1/I.scale));else{const t=(b||10)/400,r=e.clientWidth*t,n=e.clientHeight*t;X.current.scale.set(r,n,1)}Y.current=!0}}}else{const t=D.children[0];if(null!=t&&t.clientWidth&&null!=t&&t.clientHeight){const e=1/L.factor,r=t.clientWidth*e,n=t.clientHeight*e;X.current.scale.set(r,n,1),Y.current=!0}X.current.lookAt(e.camera.position)}}));const oe=c.useMemo((()=>({vertexShader:w?void 0:'\n          /*\n            This shader is from the THREE\'s SpriteMaterial.\n            We need to turn the backing plane into a Sprite\n            (make it always face the camera) if "transfrom"\n            is false.\n          */\n          #include <common>\n\n          void main() {\n            vec2 center = vec2(0., 1.);\n            float rotation = 0.0;\n\n            // This is somewhat arbitrary, but it seems to work well\n            // Need to figure out how to derive this dynamically if it even matters\n            float size = 0.03;\n\n            vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\n            vec2 scale;\n            scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );\n            scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );\n\n            bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n            if ( isPerspective ) scale *= - mvPosition.z;\n\n            vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale * size;\n            vec2 rotatedPosition;\n            rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n            rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n            mvPosition.xy += rotatedPosition;\n\n            gl_Position = projectionMatrix * mvPosition;\n          }\n      ',fragmentShader:"\n        void main() {\n          gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n        }\n      "})),[w]);return c.createElement("group",a.default({},I,{ref:Z}),E&&!ee&&c.createElement("mesh",{castShadow:S,receiveShadow:$,ref:X},z||c.createElement("planeGeometry",null),j||c.createElement("shaderMaterial",{side:n.DoubleSide,vertexShader:oe.vertexShader,fragmentShader:oe.fragmentShader})))}));exports.Html=M;
