{"version": 3, "sources": ["../src/index.ts", "../src/primitives.ts"], "sourcesContent": ["import { applyProps } from 'react-zdog'\nimport { Globals } from '@react-spring/core'\nimport { createStringInterpolator, colors } from '@react-spring/shared'\nimport { createHost } from '@react-spring/animated'\nimport { primitives } from './primitives'\nimport { WithAnimated } from './animated'\n\nGlobals.assign({\n  createStringInterpolator,\n  colors,\n})\n\nconst host = createHost(primitives, {\n  applyAnimatedValues: applyProps,\n})\n\nexport const animated = host.animated as WithAnimated\nexport { animated as a }\n\nexport * from './animated'\nexport * from '@react-spring/core'\n", "import { ElementType } from 'react'\nimport * as <PERSON><PERSON> from 'react-zdog'\n\ntype ZdogExports = typeof Zdog\ntype ZdogElements = {\n  [P in keyof ZdogExports]: P extends 'Illustration'\n    ? never\n    : ZdogExports[P] extends ElementType\n      ? P\n      : never\n}[keyof ZdogExports]\n\nexport const primitives: { [key in ZdogElements]: ElementType } = {\n  Anchor: Zdog.Anchor,\n  Shape: Zdog.Shape,\n  Group: Zdog.Group,\n  Rect: Zdog.Rect,\n  RoundedRect: Zdog.RoundedRect,\n  Ellipse: Zdog.Ellipse,\n  Polygon: Zdog.Polygon,\n  Hemisphere: Zdog.Hemisphere,\n  Cylinder: Zdog.Cylinder,\n  Cone: Zdog.Cone,\n  Box: Zdog.Box,\n}\n"], "mappings": "AAAA,OAAS,cAAAA,MAAkB,aAC3B,OAAS,WAAAC,MAAe,qBACxB,OAAS,4BAAAC,EAA0B,UAAAC,MAAc,uBACjD,OAAS,cAAAC,MAAkB,yBCF3B,UAAYC,MAAU,aAWf,IAAMC,EAAqD,CAChE,OAAa,SACb,MAAY,QACZ,MAAY,QACZ,KAAW,OACX,YAAkB,cAClB,QAAc,UACd,QAAc,UACd,WAAiB,aACjB,SAAe,WACf,KAAW,OACX,IAAU,KACZ,EDJA,WAAc,qBAbdC,EAAQ,OAAO,CACb,yBAAAC,EACA,OAAAC,CACF,CAAC,EAED,IAAMC,EAAOC,EAAWC,EAAY,CAClC,oBAAqBC,CACvB,CAAC,EAEYC,EAAWJ,EAAK", "names": ["applyProps", "Globals", "createStringInterpolator", "colors", "createHost", "Zdog", "primitives", "Globals", "createStringInterpolator", "colors", "host", "createHost", "primitives", "applyProps", "animated"]}