{"version": 3, "sources": ["../../src/index.ts", "../../src/applyAnimatedValues.ts", "../../src/AnimatedStyle.ts", "../../src/primitives.ts"], "sourcesContent": ["import { Globals } from '@react-spring/core'\nimport { unstable_batchedUpdates } from 'react-dom'\nimport { createStringInterpolator, colors } from '@react-spring/shared'\nimport { createHost } from '@react-spring/animated'\nimport { applyAnimatedValues } from './applyAnimatedValues'\nimport { AnimatedStyle } from './AnimatedStyle'\nimport { WithAnimated } from './animated'\nimport { primitives } from './primitives'\n\nGlobals.assign({\n  batchedUpdates: unstable_batchedUpdates,\n  createStringInterpolator,\n  colors,\n})\n\nconst host = createHost(primitives, {\n  applyAnimatedValues,\n  createAnimatedStyle: style => new AnimatedStyle(style),\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getComponentProps: ({ scrollTop, scrollLeft, ...props }) => props,\n})\n\nexport const animated = host.animated as WithAnimated\nexport { animated as a }\n\nexport * from './animated'\nexport * from '@react-spring/core'\n", "import { Lookup } from '@react-spring/types'\n\nconst isCustomPropRE = /^--/\n\ntype Value = string | number | boolean | null\n\nfunction dangerousStyleValue(name: string, value: Value) {\n  if (value == null || typeof value === 'boolean' || value === '') return ''\n  if (\n    typeof value === 'number' &&\n    value !== 0 &&\n    !isCustomPropRE.test(name) &&\n    !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name])\n  )\n    return value + 'px'\n  // Presumes implicit 'px' suffix for unitless numbers\n  return ('' + value).trim()\n}\n\nconst attributeCache: Lookup<string> = {}\n\ntype Instance = HTMLDivElement & { style?: Lookup }\n\nexport function applyAnimatedValues(instance: Instance, props: Lookup) {\n  if (!instance.nodeType || !instance.setAttribute) {\n    return false\n  }\n\n  const isFilterElement =\n    instance.nodeName === 'filter' ||\n    (instance.parentNode && instance.parentNode.nodeName === 'filter')\n\n  const {\n    className,\n    style,\n    children,\n    scrollTop,\n    scrollLeft,\n    viewBox,\n    ...attributes\n  } = props!\n\n  const values = Object.values(attributes)\n  const names = Object.keys(attributes).map(name =>\n    isFilterElement || instance.hasAttribute(name)\n      ? name\n      : attributeCache[name] ||\n        (attributeCache[name] = name.replace(\n          /([A-Z])/g,\n          // Attributes are written in dash case\n          n => '-' + n.toLowerCase()\n        ))\n  )\n\n  if (children !== void 0) {\n    instance.textContent = children\n  }\n\n  // Apply CSS styles\n  for (const name in style) {\n    if (style.hasOwnProperty(name)) {\n      const value = dangerousStyleValue(name, style[name])\n      if (isCustomPropRE.test(name)) {\n        instance.style.setProperty(name, value)\n      } else {\n        instance.style[name] = value\n      }\n    }\n  }\n\n  // Apply DOM attributes\n  names.forEach((name, i) => {\n    instance.setAttribute(name, values[i])\n  })\n\n  if (className !== void 0) {\n    instance.className = className\n  }\n  if (scrollTop !== void 0) {\n    instance.scrollTop = scrollTop\n  }\n  if (scrollLeft !== void 0) {\n    instance.scrollLeft = scrollLeft\n  }\n  if (viewBox !== void 0) {\n    instance.setAttribute('viewBox', viewBox)\n  }\n}\n\nlet isUnitlessNumber: { [key: string]: true } = {\n  animationIterationCount: true,\n  borderImageOutset: true,\n  borderImageSlice: true,\n  borderImageWidth: true,\n  boxFlex: true,\n  boxFlexGroup: true,\n  boxOrdinalGroup: true,\n  columnCount: true,\n  columns: true,\n  flex: true,\n  flexGrow: true,\n  flexPositive: true,\n  flexShrink: true,\n  flexNegative: true,\n  flexOrder: true,\n  gridRow: true,\n  gridRowEnd: true,\n  gridRowSpan: true,\n  gridRowStart: true,\n  gridColumn: true,\n  gridColumnEnd: true,\n  gridColumnSpan: true,\n  gridColumnStart: true,\n  fontWeight: true,\n  lineClamp: true,\n  lineHeight: true,\n  opacity: true,\n  order: true,\n  orphans: true,\n  tabSize: true,\n  widows: true,\n  zIndex: true,\n  zoom: true,\n  // SVG-related properties\n  fillOpacity: true,\n  floodOpacity: true,\n  stopOpacity: true,\n  strokeDasharray: true,\n  strokeDashoffset: true,\n  strokeMiterlimit: true,\n  strokeOpacity: true,\n  strokeWidth: true,\n}\n\nconst prefixKey = (prefix: string, key: string) =>\n  prefix + key.charAt(0).toUpperCase() + key.substring(1)\nconst prefixes = ['Webkit', 'Ms', 'Moz', 'O']\n\nisUnitlessNumber = Object.keys(isUnitlessNumber).reduce((acc, prop) => {\n  prefixes.forEach(prefix => (acc[prefixKey(prefix, prop)] = acc[prop]))\n  return acc\n}, isUnitlessNumber)\n", "import { AnimatedObject } from '@react-spring/animated'\nimport { Lookup, OneOrMore } from '@react-spring/types'\nimport {\n  is,\n  each,\n  toArray,\n  eachProp,\n  FluidValue,\n  FluidEvent,\n  getFluidValue,\n  callFluidObservers,\n  hasFluidValue,\n  addFluidObserver,\n  removeFluidObserver,\n} from '@react-spring/shared'\n\n/** The transform-functions\n * (https://developer.mozilla.org/fr/docs/Web/CSS/transform-function)\n * that you can pass as keys to your animated component style and that will be\n * animated. Perspective has been left out as it would conflict with the\n * non-transform perspective style.\n */\nconst domTransforms = /^(matrix|translate|scale|rotate|skew)/\n\n// These keys have \"px\" units by default\nconst pxTransforms = /^(translate)/\n\n// These keys have \"deg\" units by default\nconst degTransforms = /^(rotate|skew)/\n\ntype Value = number | string\n\n/** Add a unit to the value when the value is unit-less (eg: a number) */\nconst addUnit = (value: Value, unit: string): string | 0 =>\n  is.num(value) && value !== 0 ? value + unit : value\n\n/**\n * Checks if the input value matches the identity value.\n *\n *     isValueIdentity(0, 0)              // => true\n *     isValueIdentity('0px', 0)          // => true\n *     isValueIdentity([0, '0px', 0], 0)  // => true\n */\nconst isValueIdentity = (value: OneOrMore<Value>, id: number): boolean =>\n  is.arr(value)\n    ? value.every(v => isValueIdentity(v, id))\n    : is.num(value)\n      ? value === id\n      : parseFloat(value) === id\n\ntype Inputs = ReadonlyArray<Value | FluidValue<Value>>[]\ntype Transforms = ((value: any) => [string, boolean])[]\n\n/**\n * This AnimatedStyle will simplify animated components transforms by\n * interpolating all transform function passed as keys in the style object\n * including shortcuts such as x, y and z for translateX/Y/Z\n */\nexport class AnimatedStyle extends AnimatedObject {\n  constructor({ x, y, z, ...style }: Lookup) {\n    /**\n     * An array of arrays that contains the values (static or fluid)\n     * used by each transform function.\n     */\n    const inputs: Inputs = []\n    /**\n     * An array of functions that take a list of values (static or fluid)\n     * and returns (1) a CSS transform string and (2) a boolean that's true\n     * when the transform has no effect (eg: an identity transform).\n     */\n    const transforms: Transforms = []\n\n    // Combine x/y/z into translate3d\n    if (x || y || z) {\n      inputs.push([x || 0, y || 0, z || 0])\n      transforms.push((xyz: Value[]) => [\n        `translate3d(${xyz.map(v => addUnit(v, 'px')).join(',')})`, // prettier-ignore\n        isValueIdentity(xyz, 0),\n      ])\n    }\n\n    // Pluck any other transform-related props\n    eachProp(style, (value, key) => {\n      if (key === 'transform') {\n        inputs.push([value || ''])\n        transforms.push((transform: string) => [transform, transform === ''])\n      } else if (domTransforms.test(key)) {\n        delete style[key]\n        if (is.und(value)) return\n\n        const unit = pxTransforms.test(key)\n          ? 'px'\n          : degTransforms.test(key)\n            ? 'deg'\n            : ''\n\n        inputs.push(toArray(value))\n        transforms.push(\n          key === 'rotate3d'\n            ? ([x, y, z, deg]: [number, number, number, Value]) => [\n                `rotate3d(${x},${y},${z},${addUnit(deg, unit)})`,\n                isValueIdentity(deg, 0),\n              ]\n            : (input: Value[]) => [\n                `${key}(${input.map(v => addUnit(v, unit)).join(',')})`,\n                isValueIdentity(input, key.startsWith('scale') ? 1 : 0),\n              ]\n        )\n      }\n    })\n\n    if (inputs.length) {\n      style.transform = new FluidTransform(inputs, transforms)\n    }\n\n    super(style)\n  }\n}\n\n/** @internal */\nclass FluidTransform extends FluidValue<string> {\n  protected _value: string | null = null\n\n  constructor(\n    readonly inputs: Inputs,\n    readonly transforms: Transforms\n  ) {\n    super()\n  }\n\n  get() {\n    return this._value || (this._value = this._get())\n  }\n\n  protected _get() {\n    let transform = ''\n    let identity = true\n    each(this.inputs, (input, i) => {\n      const arg1 = getFluidValue(input[0])\n      const [t, id] = this.transforms[i](\n        is.arr(arg1) ? arg1 : input.map(getFluidValue)\n      )\n      transform += ' ' + t\n      identity = identity && id\n    })\n    return identity ? 'none' : transform\n  }\n\n  // Start observing our inputs once we have an observer.\n  protected observerAdded(count: number) {\n    if (count == 1)\n      each(this.inputs, input =>\n        each(\n          input,\n          value => hasFluidValue(value) && addFluidObserver(value, this)\n        )\n      )\n  }\n\n  // Stop observing our inputs once we have no observers.\n  protected observerRemoved(count: number) {\n    if (count == 0)\n      each(this.inputs, input =>\n        each(\n          input,\n          value => hasFluidValue(value) && removeFluidObserver(value, this)\n        )\n      )\n  }\n\n  eventObserved(event: FluidEvent) {\n    if (event.type == 'change') {\n      this._value = null\n    }\n    callFluidObservers(this, event)\n  }\n}\n", "export type Primitives = keyof JSX.IntrinsicElements\nexport const primitives: Primitives[] = [\n  'a',\n  'abbr',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'base',\n  'bdi',\n  'bdo',\n  'big',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'data',\n  'datalist',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'embed',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'legend',\n  'li',\n  'link',\n  'main',\n  'map',\n  'mark',\n  'menu',\n  'menuitem',\n  'meta',\n  'meter',\n  'nav',\n  'noscript',\n  'object',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'param',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'section',\n  'select',\n  'small',\n  'source',\n  'span',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'title',\n  'tr',\n  'track',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n  // SVG\n  'circle',\n  'clipPath',\n  'defs',\n  'ellipse',\n  'foreignObject',\n  'g',\n  'image',\n  'line',\n  'linearGradient',\n  'mask',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialGradient',\n  'rect',\n  'stop',\n  'svg',\n  'text',\n  'tspan',\n]\n"], "mappings": "2dAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,OAAAE,EAAA,aAAAA,IAAA,eAAAC,EAAAH,GAAA,IAAAI,EAAwB,8BACxBC,EAAwC,qBACxCC,EAAiD,gCACjDC,EAA2B,kCCD3B,IAAMC,EAAiB,MAIvB,SAASC,EAAoBC,EAAcC,EAAc,CACvD,OAAIA,GAAS,MAAQ,OAAOA,GAAU,WAAaA,IAAU,GAAW,GAEtE,OAAOA,GAAU,UACjBA,IAAU,GACV,CAACH,EAAe,KAAKE,CAAI,GACzB,EAAEE,EAAiB,eAAeF,CAAI,GAAKE,EAAiBF,CAAI,GAEzDC,EAAQ,MAET,GAAKA,GAAO,KAAK,CAC3B,CAEA,IAAME,EAAiC,CAAC,EAIjC,SAASC,EAAoBC,EAAoBC,EAAe,CACrE,GAAI,CAACD,EAAS,UAAY,CAACA,EAAS,aAClC,MAAO,GAGT,IAAME,EACJF,EAAS,WAAa,UACrBA,EAAS,YAAcA,EAAS,WAAW,WAAa,SAErD,CACJ,UAAAG,EACA,MAAAC,EACA,SAAAC,EACA,UAAAC,EACA,WAAAC,EACA,QAAAC,EACA,GAAGC,CACL,EAAIR,EAEES,EAAS,OAAO,OAAOD,CAAU,EACjCE,EAAQ,OAAO,KAAKF,CAAU,EAAE,IAAId,GACxCO,GAAmBF,EAAS,aAAaL,CAAI,EACzCA,EACAG,EAAeH,CAAI,IAClBG,EAAeH,CAAI,EAAIA,EAAK,QAC3B,WAEAiB,GAAK,IAAMA,EAAE,YAAY,CAC3B,EACN,EAEIP,IAAa,SACfL,EAAS,YAAcK,GAIzB,QAAWV,KAAQS,EACjB,GAAIA,EAAM,eAAeT,CAAI,EAAG,CAC9B,IAAMC,EAAQF,EAAoBC,EAAMS,EAAMT,CAAI,CAAC,EAC/CF,EAAe,KAAKE,CAAI,EAC1BK,EAAS,MAAM,YAAYL,EAAMC,CAAK,EAEtCI,EAAS,MAAML,CAAI,EAAIC,EAM7Be,EAAM,QAAQ,CAAChB,EAAMkB,IAAM,CACzBb,EAAS,aAAaL,EAAMe,EAAOG,CAAC,CAAC,CACvC,CAAC,EAEGV,IAAc,SAChBH,EAAS,UAAYG,GAEnBG,IAAc,SAChBN,EAAS,UAAYM,GAEnBC,IAAe,SACjBP,EAAS,WAAaO,GAEpBC,IAAY,QACdR,EAAS,aAAa,UAAWQ,CAAO,CAE5C,CAEA,IAAIX,EAA4C,CAC9C,wBAAyB,GACzB,kBAAmB,GACnB,iBAAkB,GAClB,iBAAkB,GAClB,QAAS,GACT,aAAc,GACd,gBAAiB,GACjB,YAAa,GACb,QAAS,GACT,KAAM,GACN,SAAU,GACV,aAAc,GACd,WAAY,GACZ,aAAc,GACd,UAAW,GACX,QAAS,GACT,WAAY,GACZ,YAAa,GACb,aAAc,GACd,WAAY,GACZ,cAAe,GACf,eAAgB,GAChB,gBAAiB,GACjB,WAAY,GACZ,UAAW,GACX,WAAY,GACZ,QAAS,GACT,MAAO,GACP,QAAS,GACT,QAAS,GACT,OAAQ,GACR,OAAQ,GACR,KAAM,GAEN,YAAa,GACb,aAAc,GACd,YAAa,GACb,gBAAiB,GACjB,iBAAkB,GAClB,iBAAkB,GAClB,cAAe,GACf,YAAa,EACf,EAEMiB,EAAY,CAACC,EAAgBC,IACjCD,EAASC,EAAI,OAAO,CAAC,EAAE,YAAY,EAAIA,EAAI,UAAU,CAAC,EAClDC,EAAW,CAAC,SAAU,KAAM,MAAO,GAAG,EAE5CpB,EAAmB,OAAO,KAAKA,CAAgB,EAAE,OAAO,CAACqB,EAAKC,KAC5DF,EAAS,QAAQF,GAAWG,EAAIJ,EAAUC,EAAQI,CAAI,CAAC,EAAID,EAAIC,CAAI,CAAE,EAC9DD,GACNrB,CAAgB,EC7InB,IAAAuB,EAA+B,kCAE/BC,EAYO,gCAQDC,EAAgB,wCAGhBC,EAAe,eAGfC,EAAgB,iBAKhBC,EAAU,CAACC,EAAcC,IAC7B,KAAG,IAAID,CAAK,GAAKA,IAAU,EAAIA,EAAQC,EAAOD,EAS1CE,EAAkB,CAACF,EAAyBG,IAChD,KAAG,IAAIH,CAAK,EACRA,EAAM,MAAMI,GAAKF,EAAgBE,EAAGD,CAAE,CAAC,EACvC,KAAG,IAAIH,CAAK,EACVA,IAAUG,EACV,WAAWH,CAAK,IAAMG,EAUjBE,EAAN,cAA4B,gBAAe,CAChD,YAAY,CAAE,EAAAC,EAAG,EAAAC,EAAG,EAAAC,EAAG,GAAGC,CAAM,EAAW,CAKzC,IAAMC,EAAiB,CAAC,EAMlBC,EAAyB,CAAC,GAG5BL,GAAKC,GAAKC,KACZE,EAAO,KAAK,CAACJ,GAAK,EAAGC,GAAK,EAAGC,GAAK,CAAC,CAAC,EACpCG,EAAW,KAAMC,GAAiB,CAChC,eAAeA,EAAI,IAAIR,GAAKL,EAAQK,EAAG,IAAI,CAAC,EAAE,KAAK,GAAG,KACtDF,EAAgBU,EAAK,CAAC,CACxB,CAAC,MAIH,YAASH,EAAO,CAACT,EAAOa,IAAQ,CAC9B,GAAIA,IAAQ,YACVH,EAAO,KAAK,CAACV,GAAS,EAAE,CAAC,EACzBW,EAAW,KAAMG,GAAsB,CAACA,EAAWA,IAAc,EAAE,CAAC,UAC3DlB,EAAc,KAAKiB,CAAG,EAAG,CAElC,GADA,OAAOJ,EAAMI,CAAG,EACZ,KAAG,IAAIb,CAAK,EAAG,OAEnB,IAAMC,EAAOJ,EAAa,KAAKgB,CAAG,EAC9B,KACAf,EAAc,KAAKe,CAAG,EACpB,MACA,GAENH,EAAO,QAAK,WAAQV,CAAK,CAAC,EAC1BW,EAAW,KACTE,IAAQ,WACJ,CAAC,CAACP,EAAGC,EAAGC,EAAGO,CAAG,IAAuC,CACnD,YAAYT,KAAKC,KAAKC,KAAKT,EAAQgB,EAAKd,CAAI,KAC5CC,EAAgBa,EAAK,CAAC,CACxB,EACCC,GAAmB,CAClB,GAAGH,KAAOG,EAAM,IAAIZ,GAAKL,EAAQK,EAAGH,CAAI,CAAC,EAAE,KAAK,GAAG,KACnDC,EAAgBc,EAAOH,EAAI,WAAW,OAAO,EAAI,EAAI,CAAC,CACxD,CACN,EAEJ,CAAC,EAEGH,EAAO,SACTD,EAAM,UAAY,IAAIQ,EAAeP,EAAQC,CAAU,GAGzD,MAAMF,CAAK,CACb,CACF,EAGMQ,EAAN,cAA6B,YAAmB,CAG9C,YACWP,EACAC,EACT,CACA,MAAM,EAHG,YAAAD,EACA,gBAAAC,EAJX,KAAU,OAAwB,IAOlC,CAEA,KAAM,CACJ,OAAO,KAAK,SAAW,KAAK,OAAS,KAAK,KAAK,EACjD,CAEU,MAAO,CACf,IAAIG,EAAY,GACZI,EAAW,GACf,iBAAK,KAAK,OAAQ,CAACF,EAAOG,IAAM,CAC9B,IAAMC,KAAO,iBAAcJ,EAAM,CAAC,CAAC,EAC7B,CAACK,EAAGlB,CAAE,EAAI,KAAK,WAAWgB,CAAC,EAC/B,KAAG,IAAIC,CAAI,EAAIA,EAAOJ,EAAM,IAAI,eAAa,CAC/C,EACAF,GAAa,IAAMO,EACnBH,EAAWA,GAAYf,CACzB,CAAC,EACMe,EAAW,OAASJ,CAC7B,CAGU,cAAcQ,EAAe,CACjCA,GAAS,MACX,QAAK,KAAK,OAAQN,MAChB,QACEA,EACAhB,MAAS,iBAAcA,CAAK,MAAK,oBAAiBA,EAAO,IAAI,CAC/D,CACF,CACJ,CAGU,gBAAgBsB,EAAe,CACnCA,GAAS,MACX,QAAK,KAAK,OAAQN,MAChB,QACEA,EACAhB,MAAS,iBAAcA,CAAK,MAAK,uBAAoBA,EAAO,IAAI,CAClE,CACF,CACJ,CAEA,cAAcuB,EAAmB,CAC3BA,EAAM,MAAQ,WAChB,KAAK,OAAS,SAEhB,sBAAmB,KAAMA,CAAK,CAChC,CACF,EC/KO,IAAMC,EAA2B,CACtC,IACA,OACA,UACA,OACA,UACA,QACA,QACA,IACA,OACA,MACA,MACA,MACA,aACA,OACA,KACA,SACA,SACA,UACA,OACA,OACA,MACA,WACA,OACA,WACA,KACA,MACA,UACA,MACA,SACA,MACA,KACA,KACA,KACA,QACA,WACA,aACA,SACA,SACA,OACA,KACA,KACA,KACA,KACA,KACA,KACA,OACA,SACA,SACA,KACA,OACA,IACA,SACA,MACA,QACA,MACA,MACA,SACA,QACA,SACA,KACA,OACA,OACA,MACA,OACA,OACA,WACA,OACA,QACA,MACA,WACA,SACA,KACA,WACA,SACA,SACA,IACA,QACA,UACA,MACA,WACA,IACA,KACA,KACA,OACA,IACA,OACA,SACA,UACA,SACA,QACA,SACA,OACA,SACA,QACA,MACA,UACA,MACA,QACA,QACA,KACA,WACA,QACA,KACA,QACA,OACA,QACA,KACA,QACA,IACA,KACA,MACA,QACA,MAEA,SACA,WACA,OACA,UACA,gBACA,IACA,QACA,OACA,iBACA,OACA,OACA,UACA,UACA,WACA,iBACA,OACA,OACA,MACA,OACA,OACF,EH9GAC,EAAAC,EAAc,8BA1Bd,gBASA,UAAQ,OAAO,CACb,eAAgB,0BAChB,oDACA,eACF,CAAC,EAED,IAAMC,KAAO,cAAWC,EAAY,CAClC,oBAAAC,EACA,oBAAqBC,GAAS,IAAIC,EAAcD,CAAK,EAErD,kBAAmB,CAAC,CAAE,UAAAE,EAAW,WAAAC,EAAY,GAAGC,CAAM,IAAMA,CAC9D,CAAC,EAEYC,EAAWR,EAAK", "names": ["src_exports", "__export", "animated", "__toCommonJS", "import_core", "import_react_dom", "import_shared", "import_animated", "isCustomPropRE", "dangerousStyleValue", "name", "value", "isUnitlessNumber", "attributeCache", "applyAnimatedValues", "instance", "props", "isFilterElement", "className", "style", "children", "scrollTop", "scrollLeft", "viewBox", "attributes", "values", "names", "n", "i", "prefixKey", "prefix", "key", "prefixes", "acc", "prop", "import_animated", "import_shared", "domTransforms", "pxTransforms", "degTransforms", "addUnit", "value", "unit", "isValueIdentity", "id", "v", "AnimatedStyle", "x", "y", "z", "style", "inputs", "transforms", "xyz", "key", "transform", "deg", "input", "FluidTransform", "identity", "i", "arg1", "t", "count", "event", "primitives", "__reExport", "src_exports", "host", "primitives", "applyAnimatedValues", "style", "AnimatedStyle", "scrollTop", "scrollLeft", "props", "animated"]}