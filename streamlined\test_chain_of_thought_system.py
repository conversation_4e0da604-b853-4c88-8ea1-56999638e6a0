"""
Test Suite for A.T.L.A.S Chain-of-Thought Trading System
Comprehensive testing of all Chain-of-Thought components
"""

import asyncio
import pytest
import logging
from datetime import datetime
from typing import Dict, List, Any

from .cot_trading_orchestrator import ChainOfThoughtTradingOrchestrator
from .chain_of_thought_engine import ChainOfThoughtEngine
from .profit_strategy_engine import ProfitTargetedStrategyEngine
from .risk_management_engine import RiskManagementEngine, RiskManagementProfile
from .options_education_engine import OptionsEducationEngine
from .execution_monitoring_engine import ExecutionMonitoringEngine
from .models import ChainOfThoughtAnalysis, ProfitTargetedStrategy


class TestChainOfThoughtSystem:
    """Test suite for Chain-of-Thought trading system"""
    
    @pytest.fixture
    def orchestrator(self):
        """Create orchestrator instance for testing"""
        return ChainOfThoughtTradingOrchestrator()
    
    @pytest.fixture
    def sample_account_size(self):
        """Sample account size for testing"""
        return 50000.0
    
    @pytest.mark.asyncio
    async def test_chain_of_thought_analysis(self):
        """Test Chain-of-Thought analysis engine"""
        cot_engine = ChainOfThoughtEngine()
        
        # Test TTM Squeeze analysis
        analysis = await cot_engine.analyze_ttm_squeeze_with_cot("AAPL")
        
        if analysis:  # Only test if we have data
            assert isinstance(analysis, ChainOfThoughtAnalysis)
            assert analysis.symbol == "AAPL"
            assert len(analysis.steps) >= 5  # Should have at least 5 analysis steps
            assert 0.0 <= analysis.final_confidence <= 1.0
            assert len(analysis.educational_notes) > 0
            
            # Check that each step has required fields
            for step in analysis.steps:
                assert step.step_number > 0
                assert step.category in ["technical", "momentum", "volume", "multi_timeframe", "risk", "final"]
                assert len(step.explanation) > 0
                assert step.confidence_contribution >= 0.0
        
        print("✅ Chain-of-Thought analysis test passed")
    
    @pytest.mark.asyncio
    async def test_profit_targeted_strategy(self):
        """Test profit-targeted strategy engine"""
        strategy_engine = ProfitTargetedStrategyEngine()
        
        # Test strategy creation
        strategy = await strategy_engine.create_profit_targeted_strategy(
            profit_target=500.0,
            account_size=50000.0,
            timeframe="intraday",
            risk_tolerance="moderate"
        )
        
        if strategy:  # Only test if strategy was created
            assert isinstance(strategy, ProfitTargetedStrategy)
            assert strategy.profit_target == 500.0
            assert strategy.timeframe == "intraday"
            assert strategy.target_positions >= 2
            assert len(strategy.symbols_to_scan) > 0
            assert 0.0 < strategy.kelly_fraction <= 0.25
            assert len(strategy.strategy_reasoning) > 0
        
        print("✅ Profit-targeted strategy test passed")
    
    def test_risk_management_engine(self):
        """Test risk management engine"""
        risk_engine = RiskManagementEngine()
        
        # Test position sizing calculation
        position_calc = risk_engine.calculate_position_size(
            symbol="AAPL",
            entry_price=150.0,
            account_size=50000.0,
            confidence=0.75,
            risk_profile=RiskManagementProfile(account_size=50000.0)
        )
        
        assert position_calc.recommended_shares > 0
        assert position_calc.dollar_amount > 0
        assert position_calc.risk_amount > 0
        assert position_calc.stop_loss_price < 150.0  # Stop should be below entry
        assert position_calc.target_price > 150.0     # Target should be above entry
        assert position_calc.risk_reward_ratio > 0
        assert len(position_calc.educational_explanation) > 0
        
        print("✅ Risk management engine test passed")
    
    def test_options_education_engine(self):
        """Test options education engine"""
        options_engine = OptionsEducationEngine()
        
        # Test options analysis
        analysis = options_engine.analyze_options_educational(
            symbol="AAPL",
            option_type="call",
            strike=150.0,
            expiration=datetime(2024, 12, 20),
            stock_price=155.0,
            option_price=8.50,
            implied_volatility=0.25,
            volume=1000,
            open_interest=5000
        )
        
        assert analysis.symbol == "AAPL"
        assert analysis.option_type == "call"
        assert analysis.strike == 150.0
        assert analysis.greeks.delta > 0  # Call delta should be positive
        assert len(analysis.beginner_summary) > 0
        assert len(analysis.advanced_insights) > 0
        assert len(analysis.risk_assessment.warnings) >= 0
        
        print("✅ Options education engine test passed")
    
    @pytest.mark.asyncio
    async def test_execution_monitoring_engine(self):
        """Test execution and monitoring engine"""
        execution_engine = ExecutionMonitoringEngine()
        
        # Test portfolio monitoring
        monitoring = await execution_engine.monitor_portfolio_realtime(50000.0)
        
        assert monitoring.total_value > 0
        assert isinstance(monitoring.daily_pnl, float)
        assert isinstance(monitoring.daily_pnl_percent, float)
        assert monitoring.open_positions >= 0
        assert monitoring.risk_limit_remaining >= 0
        assert len(monitoring.performance_summary) > 0
        
        print("✅ Execution monitoring engine test passed")
    
    @pytest.mark.asyncio
    async def test_comprehensive_trading_plan(self, orchestrator, sample_account_size):
        """Test comprehensive trading plan creation"""
        
        plan = await orchestrator.create_comprehensive_trading_plan(
            user_request="Make me $300 today",
            account_size=sample_account_size,
            risk_tolerance="moderate"
        )
        
        assert plan["success"] in [True, False]  # Should have success indicator
        
        if plan["success"]:
            assert "strategy" in plan
            assert "trade_opportunities" in plan
            assert "comprehensive_plan" in plan
            assert "safety_notes" in plan
            assert "educational_summary" in plan
            
            # Check strategy
            strategy = plan["strategy"]
            assert strategy.profit_target > 0
            assert strategy.timeframe in ["intraday", "swing", "position"]
            
            # Check trade opportunities
            opportunities = plan["trade_opportunities"]
            assert isinstance(opportunities, list)
            
            # Check safety notes
            safety_notes = plan["safety_notes"]
            assert isinstance(safety_notes, list)
            assert len(safety_notes) > 0
        
        print("✅ Comprehensive trading plan test passed")
    
    @pytest.mark.asyncio
    async def test_full_cot_trade_execution(self, orchestrator, sample_account_size):
        """Test full Chain-of-Thought trade execution"""
        
        result = await orchestrator.execute_trade_with_full_cot(
            symbol="AAPL",
            account_size=sample_account_size,
            confidence_override=None
        )
        
        assert "success" in result
        
        if result["success"]:
            assert "chain_of_thought" in result
            assert "position_sizing" in result
            assert "validation" in result
            assert "execution" in result
            assert "educational_summary" in result
            
            # Check Chain-of-Thought analysis
            cot = result["chain_of_thought"]
            assert len(cot.steps) >= 5
            assert 0.0 <= cot.final_confidence <= 1.0
            
            # Check position sizing
            position_sizing = result["position_sizing"]
            assert position_sizing.recommended_shares > 0
            assert position_sizing.risk_amount > 0
            
            # Check validation
            validation = result["validation"]
            assert isinstance(validation.is_valid, bool)
            assert 0.0 <= validation.confidence_score <= 1.0
        
        print("✅ Full CoT trade execution test passed")
    
    @pytest.mark.asyncio
    async def test_portfolio_dashboard(self, orchestrator, sample_account_size):
        """Test portfolio dashboard generation"""
        
        dashboard = await orchestrator.get_portfolio_dashboard(sample_account_size)
        
        if "error" not in dashboard:
            assert "portfolio_monitoring" in dashboard
            assert "market_conditions" in dashboard
            assert "risk_analysis" in dashboard
            assert "educational_insights" in dashboard
            assert "safety_status" in dashboard
            assert "next_actions" in dashboard
            
            # Check portfolio monitoring
            portfolio = dashboard["portfolio_monitoring"]
            assert portfolio.total_value > 0
            
            # Check market conditions
            market = dashboard["market_conditions"]
            assert "condition" in market
            assert "trading_allowed" in market
            
            # Check risk analysis
            risk = dashboard["risk_analysis"]
            assert "daily_risk_used" in risk
            assert "risk_utilization_percent" in risk
            
            # Check educational insights
            insights = dashboard["educational_insights"]
            assert isinstance(insights, list)
            
            # Check safety status
            safety = dashboard["safety_status"]
            assert "safety_score" in safety
            assert "status" in safety
            
            # Check next actions
            actions = dashboard["next_actions"]
            assert isinstance(actions, list)
        
        print("✅ Portfolio dashboard test passed")
    
    def test_educational_explanations(self):
        """Test that all components provide educational explanations"""
        
        # Test Chain-of-Thought analogies
        cot_engine = ChainOfThoughtEngine()
        assert len(cot_engine.analogies) > 0
        for analogy in cot_engine.analogies.values():
            assert len(analogy) > 50  # Should be substantial explanations
        
        # Test options education analogies
        options_engine = OptionsEducationEngine()
        assert len(options_engine.analogies) > 0
        for analogy in options_engine.analogies.values():
            assert len(analogy) > 30  # Should be meaningful explanations
        
        print("✅ Educational explanations test passed")
    
    def test_safety_guardrails(self, orchestrator):
        """Test safety guardrails are in place"""
        
        guardrails = orchestrator.safety_guardrails
        
        # Check essential safety limits
        assert guardrails["daily_loss_limit_percent"] <= 5.0  # Max 5% daily loss
        assert guardrails["max_position_correlation"] <= 1.0  # Max 100% correlation
        assert guardrails["vix_trading_threshold"] >= 30.0    # Reasonable VIX threshold
        assert guardrails["min_confidence_threshold"] >= 0.5  # Minimum 50% confidence
        assert guardrails["max_positions"] <= 10              # Reasonable position limit
        
        print("✅ Safety guardrails test passed")


async def run_comprehensive_test():
    """Run comprehensive test suite"""
    print("🧪 Starting A.T.L.A.S Chain-of-Thought System Tests")
    print("=" * 60)
    
    test_suite = TestChainOfThoughtSystem()
    
    try:
        # Test individual components
        await test_suite.test_chain_of_thought_analysis()
        await test_suite.test_profit_targeted_strategy()
        test_suite.test_risk_management_engine()
        test_suite.test_options_education_engine()
        await test_suite.test_execution_monitoring_engine()
        
        # Test integrated system
        orchestrator = ChainOfThoughtTradingOrchestrator()
        account_size = 50000.0
        
        await test_suite.test_comprehensive_trading_plan(orchestrator, account_size)
        await test_suite.test_full_cot_trade_execution(orchestrator, account_size)
        await test_suite.test_portfolio_dashboard(orchestrator, account_size)
        
        # Test educational and safety features
        test_suite.test_educational_explanations()
        test_suite.test_safety_guardrails(orchestrator)
        
        print("=" * 60)
        print("🎉 ALL TESTS PASSED! Chain-of-Thought system is ready for deployment.")
        print("🛡️ Safety guardrails are active")
        print("📚 Educational features are working")
        print("🧠 Chain-of-Thought analysis is operational")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    # Run the comprehensive test
    asyncio.run(run_comprehensive_test())
