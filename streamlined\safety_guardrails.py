"""
A.T.L.A.S Safety Guardrails System
Comprehensive safety measures and validation for trading operations
"""

import logging
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from .config import settings
from .models import Position, Quote, ChainOfThoughtAnalysis


class SafetyLevel(Enum):
    SAFE = "safe"
    CAUTION = "caution"
    WARNING = "warning"
    DANGER = "danger"
    BLOCKED = "blocked"


@dataclass
class SafetyCheck:
    """Individual safety check result"""
    check_name: str
    status: SafetyLevel
    message: str
    educational_note: str
    blocking: bool = False


@dataclass
class SafetyAssessment:
    """Complete safety assessment"""
    overall_status: SafetyLevel
    safety_score: float  # 0-100
    checks: List[SafetyCheck]
    blocking_issues: List[str]
    warnings: List[str]
    educational_summary: str
    trading_allowed: bool


class SafetyGuardrailsEngine:
    """
    Comprehensive safety guardrails system with educational explanations
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Core safety limits (non-negotiable)
        self.HARD_LIMITS = {
            "max_daily_loss_percent": 3.0,      # 3% maximum daily loss
            "max_position_size_percent": 20.0,   # 20% maximum single position
            "max_correlation": 0.85,             # 85% maximum correlation
            "min_confidence_threshold": 0.70,    # 70% minimum confidence
            "max_positions": 6,                  # Maximum 6 open positions
            "vix_danger_threshold": 40.0,        # VIX level to stop trading
            "min_account_size": 1000.0,          # Minimum $1,000 account
            "max_leverage": 1.0                  # No leverage allowed
        }
        
        # Soft limits (warnings but not blocking)
        self.SOFT_LIMITS = {
            "recommended_daily_loss_percent": 2.0,  # 2% recommended daily loss
            "recommended_position_size_percent": 10.0,  # 10% recommended position
            "vix_caution_threshold": 25.0,         # VIX level for caution
            "min_volume_threshold": 100000,        # Minimum daily volume
            "earnings_proximity_days": 3,          # Days before earnings
            "market_close_buffer_minutes": 30      # Minutes before close
        }
        
        # Trading hours (EST)
        self.MARKET_OPEN = time(9, 30)
        self.MARKET_CLOSE = time(16, 0)
        self.SAFE_TRADING_START = time(10, 0)   # Avoid opening volatility
        self.SAFE_TRADING_END = time(15, 30)    # Avoid closing volatility
        
        # Daily tracking (would be persistent in production)
        self.daily_stats = {
            "trades_today": 0,
            "daily_pnl": 0.0,
            "max_drawdown_today": 0.0,
            "positions_opened": 0,
            "risk_used": 0.0
        }
    
    def comprehensive_safety_check(self, 
                                 symbol: str,
                                 position_size_dollars: float,
                                 account_size: float,
                                 confidence: float,
                                 current_positions: List[Position],
                                 cot_analysis: Optional[ChainOfThoughtAnalysis] = None,
                                 market_data: Optional[Dict[str, Any]] = None) -> SafetyAssessment:
        """
        Perform comprehensive safety assessment
        """
        
        checks = []
        blocking_issues = []
        warnings = []
        
        # 1. Account Size Validation
        check = self._check_account_size(account_size)
        checks.append(check)
        if check.blocking:
            blocking_issues.append(check.message)
        
        # 2. Daily Loss Limits
        check = self._check_daily_loss_limits(account_size)
        checks.append(check)
        if check.blocking:
            blocking_issues.append(check.message)
        elif check.status in [SafetyLevel.WARNING, SafetyLevel.CAUTION]:
            warnings.append(check.message)
        
        # 3. Position Size Limits
        check = self._check_position_size_limits(position_size_dollars, account_size)
        checks.append(check)
        if check.blocking:
            blocking_issues.append(check.message)
        elif check.status in [SafetyLevel.WARNING, SafetyLevel.CAUTION]:
            warnings.append(check.message)
        
        # 4. Portfolio Concentration
        check = self._check_portfolio_concentration(symbol, position_size_dollars, current_positions, account_size)
        checks.append(check)
        if check.blocking:
            blocking_issues.append(check.message)
        elif check.status in [SafetyLevel.WARNING, SafetyLevel.CAUTION]:
            warnings.append(check.message)
        
        # 5. Confidence Threshold
        check = self._check_confidence_threshold(confidence)
        checks.append(check)
        if check.blocking:
            blocking_issues.append(check.message)
        elif check.status in [SafetyLevel.WARNING, SafetyLevel.CAUTION]:
            warnings.append(check.message)
        
        # 6. Market Conditions
        check = self._check_market_conditions(market_data)
        checks.append(check)
        if check.blocking:
            blocking_issues.append(check.message)
        elif check.status in [SafetyLevel.WARNING, SafetyLevel.CAUTION]:
            warnings.append(check.message)
        
        # 7. Trading Hours
        check = self._check_trading_hours()
        checks.append(check)
        if check.blocking:
            blocking_issues.append(check.message)
        elif check.status in [SafetyLevel.WARNING, SafetyLevel.CAUTION]:
            warnings.append(check.message)
        
        # 8. Maximum Positions
        check = self._check_max_positions(current_positions)
        checks.append(check)
        if check.blocking:
            blocking_issues.append(check.message)
        
        # Calculate overall safety score and status
        safety_score = self._calculate_safety_score(checks)
        overall_status = self._determine_overall_status(checks, blocking_issues)
        trading_allowed = len(blocking_issues) == 0
        
        # Generate educational summary
        educational_summary = self._generate_educational_summary(
            overall_status, safety_score, len(blocking_issues), len(warnings)
        )
        
        return SafetyAssessment(
            overall_status=overall_status,
            safety_score=safety_score,
            checks=checks,
            blocking_issues=blocking_issues,
            warnings=warnings,
            educational_summary=educational_summary,
            trading_allowed=trading_allowed
        )
    
    def _check_account_size(self, account_size: float) -> SafetyCheck:
        """Check minimum account size requirements"""
        
        if account_size < self.HARD_LIMITS["min_account_size"]:
            return SafetyCheck(
                check_name="Account Size",
                status=SafetyLevel.BLOCKED,
                message=f"Account size ${account_size:,.2f} below minimum ${self.HARD_LIMITS['min_account_size']:,.2f}",
                educational_note="Minimum account size protects against over-leveraging small accounts",
                blocking=True
            )
        elif account_size < 10000:
            return SafetyCheck(
                check_name="Account Size",
                status=SafetyLevel.CAUTION,
                message=f"Small account size ${account_size:,.2f} - extra caution recommended",
                educational_note="Smaller accounts should use more conservative position sizing"
            )
        else:
            return SafetyCheck(
                check_name="Account Size",
                status=SafetyLevel.SAFE,
                message=f"Account size ${account_size:,.2f} is adequate for trading",
                educational_note="Sufficient account size allows for proper diversification"
            )
    
    def _check_daily_loss_limits(self, account_size: float) -> SafetyCheck:
        """Check daily loss limits"""
        
        daily_loss_percent = abs(min(self.daily_stats["daily_pnl"], 0)) / account_size * 100
        hard_limit = self.HARD_LIMITS["max_daily_loss_percent"]
        soft_limit = self.SOFT_LIMITS["recommended_daily_loss_percent"]
        
        if daily_loss_percent >= hard_limit:
            return SafetyCheck(
                check_name="Daily Loss Limit",
                status=SafetyLevel.BLOCKED,
                message=f"Daily loss limit exceeded: {daily_loss_percent:.1f}% >= {hard_limit:.1f}%",
                educational_note="Daily loss limits prevent catastrophic losses and emotional trading",
                blocking=True
            )
        elif daily_loss_percent >= soft_limit:
            return SafetyCheck(
                check_name="Daily Loss Limit",
                status=SafetyLevel.WARNING,
                message=f"Approaching daily loss limit: {daily_loss_percent:.1f}% of {hard_limit:.1f}%",
                educational_note="Consider stopping trading for today to preserve capital"
            )
        else:
            remaining = hard_limit - daily_loss_percent
            return SafetyCheck(
                check_name="Daily Loss Limit",
                status=SafetyLevel.SAFE,
                message=f"Daily loss: {daily_loss_percent:.1f}%, {remaining:.1f}% remaining",
                educational_note="Daily loss limits help maintain long-term profitability"
            )
    
    def _check_position_size_limits(self, position_size: float, account_size: float) -> SafetyCheck:
        """Check position size limits"""
        
        position_percent = (position_size / account_size) * 100
        hard_limit = self.HARD_LIMITS["max_position_size_percent"]
        soft_limit = self.SOFT_LIMITS["recommended_position_size_percent"]
        
        if position_percent > hard_limit:
            return SafetyCheck(
                check_name="Position Size",
                status=SafetyLevel.BLOCKED,
                message=f"Position size {position_percent:.1f}% exceeds maximum {hard_limit:.1f}%",
                educational_note="Large positions create concentration risk - diversification is key",
                blocking=True
            )
        elif position_percent > soft_limit:
            return SafetyCheck(
                check_name="Position Size",
                status=SafetyLevel.CAUTION,
                message=f"Large position size: {position_percent:.1f}% of account",
                educational_note="Consider reducing position size for better risk management"
            )
        else:
            return SafetyCheck(
                check_name="Position Size",
                status=SafetyLevel.SAFE,
                message=f"Position size {position_percent:.1f}% is within safe limits",
                educational_note="Appropriate position sizing is crucial for long-term success"
            )
    
    def _check_portfolio_concentration(self, symbol: str, position_size: float,
                                     current_positions: List[Position], account_size: float) -> SafetyCheck:
        """Check portfolio concentration and correlation"""
        
        # Calculate sector exposure (simplified)
        sector_exposure = self._calculate_sector_exposure(symbol, current_positions, position_size, account_size)
        max_correlation = self.HARD_LIMITS["max_correlation"]
        
        if sector_exposure > max_correlation:
            return SafetyCheck(
                check_name="Portfolio Concentration",
                status=SafetyLevel.BLOCKED,
                message=f"Sector concentration {sector_exposure*100:.0f}% exceeds limit {max_correlation*100:.0f}%",
                educational_note="Diversification across sectors reduces correlation risk",
                blocking=True
            )
        elif sector_exposure > 0.6:  # 60% warning threshold
            return SafetyCheck(
                check_name="Portfolio Concentration",
                status=SafetyLevel.WARNING,
                message=f"High sector concentration: {sector_exposure*100:.0f}%",
                educational_note="Consider diversifying across different sectors"
            )
        else:
            return SafetyCheck(
                check_name="Portfolio Concentration",
                status=SafetyLevel.SAFE,
                message=f"Portfolio diversification adequate: {sector_exposure*100:.0f}% sector exposure",
                educational_note="Good diversification reduces overall portfolio risk"
            )
    
    def _check_confidence_threshold(self, confidence: float) -> SafetyCheck:
        """Check signal confidence threshold"""
        
        min_confidence = self.HARD_LIMITS["min_confidence_threshold"]
        
        if confidence < min_confidence:
            return SafetyCheck(
                check_name="Signal Confidence",
                status=SafetyLevel.BLOCKED,
                message=f"Signal confidence {confidence*100:.0f}% below minimum {min_confidence*100:.0f}%",
                educational_note="High-confidence signals have better success rates",
                blocking=True
            )
        elif confidence < 0.8:
            return SafetyCheck(
                check_name="Signal Confidence",
                status=SafetyLevel.CAUTION,
                message=f"Moderate confidence: {confidence*100:.0f}%",
                educational_note="Consider reducing position size for lower confidence signals"
            )
        else:
            return SafetyCheck(
                check_name="Signal Confidence",
                status=SafetyLevel.SAFE,
                message=f"High confidence signal: {confidence*100:.0f}%",
                educational_note="High-confidence signals justify normal position sizing"
            )
    
    def _check_market_conditions(self, market_data: Optional[Dict[str, Any]]) -> SafetyCheck:
        """Check market conditions and volatility"""
        
        if not market_data:
            vix = 20.0  # Default assumption
        else:
            vix = market_data.get("vix", 20.0)
        
        danger_threshold = self.HARD_LIMITS["vix_danger_threshold"]
        caution_threshold = self.SOFT_LIMITS["vix_caution_threshold"]
        
        if vix >= danger_threshold:
            return SafetyCheck(
                check_name="Market Conditions",
                status=SafetyLevel.BLOCKED,
                message=f"Extreme volatility: VIX {vix:.1f} >= {danger_threshold:.1f}",
                educational_note="High volatility periods require extra caution - wait for calmer markets",
                blocking=True
            )
        elif vix >= caution_threshold:
            return SafetyCheck(
                check_name="Market Conditions",
                status=SafetyLevel.CAUTION,
                message=f"Elevated volatility: VIX {vix:.1f}",
                educational_note="Consider reducing position sizes during volatile periods"
            )
        else:
            return SafetyCheck(
                check_name="Market Conditions",
                status=SafetyLevel.SAFE,
                message=f"Normal market conditions: VIX {vix:.1f}",
                educational_note="Calm markets are generally better for trading"
            )
    
    def _check_trading_hours(self) -> SafetyCheck:
        """Check trading hours and market timing"""
        
        current_time = datetime.now().time()
        
        if current_time < self.MARKET_OPEN or current_time > self.MARKET_CLOSE:
            return SafetyCheck(
                check_name="Trading Hours",
                status=SafetyLevel.BLOCKED,
                message="Market is closed",
                educational_note="Trading only allowed during market hours for liquidity",
                blocking=True
            )
        elif current_time < self.SAFE_TRADING_START:
            return SafetyCheck(
                check_name="Trading Hours",
                status=SafetyLevel.CAUTION,
                message="Early market hours - higher volatility",
                educational_note="First 30 minutes can be volatile due to overnight news"
            )
        elif current_time > self.SAFE_TRADING_END:
            return SafetyCheck(
                check_name="Trading Hours",
                status=SafetyLevel.CAUTION,
                message="Late market hours - approaching close",
                educational_note="Final 30 minutes can be volatile and illiquid"
            )
        else:
            return SafetyCheck(
                check_name="Trading Hours",
                status=SafetyLevel.SAFE,
                message="Optimal trading hours",
                educational_note="Mid-day hours typically have best liquidity and stability"
            )
    
    def _check_max_positions(self, current_positions: List[Position]) -> SafetyCheck:
        """Check maximum position limits"""
        
        position_count = len(current_positions)
        max_positions = self.HARD_LIMITS["max_positions"]
        
        if position_count >= max_positions:
            return SafetyCheck(
                check_name="Position Count",
                status=SafetyLevel.BLOCKED,
                message=f"Maximum positions reached: {position_count}/{max_positions}",
                educational_note="Position limits prevent over-diversification and improve focus",
                blocking=True
            )
        elif position_count >= max_positions - 1:
            return SafetyCheck(
                check_name="Position Count",
                status=SafetyLevel.WARNING,
                message=f"Near maximum positions: {position_count}/{max_positions}",
                educational_note="Consider closing some positions before opening new ones"
            )
        else:
            return SafetyCheck(
                check_name="Position Count",
                status=SafetyLevel.SAFE,
                message=f"Position count: {position_count}/{max_positions}",
                educational_note="Manageable number of positions allows proper monitoring"
            )
    
    def _calculate_sector_exposure(self, symbol: str, positions: List[Position],
                                 new_position_size: float, account_size: float) -> float:
        """Calculate sector exposure (simplified)"""
        
        # Simplified sector mapping
        tech_stocks = ["AAPL", "MSFT", "GOOGL", "AMZN", "META", "NVDA", "NFLX"]
        finance_stocks = ["JPM", "BAC", "WFC", "GS", "MS", "C"]
        
        symbol_sector = None
        if symbol in tech_stocks:
            symbol_sector = "tech"
        elif symbol in finance_stocks:
            symbol_sector = "finance"
        
        if not symbol_sector:
            return 0.0  # Unknown sector
        
        # Calculate current sector exposure
        sector_value = new_position_size  # New position
        total_value = new_position_size
        
        for pos in positions:
            pos_sector = None
            if pos.symbol in tech_stocks:
                pos_sector = "tech"
            elif pos.symbol in finance_stocks:
                pos_sector = "finance"
            
            if pos_sector == symbol_sector:
                sector_value += abs(pos.market_value)
            total_value += abs(pos.market_value)
        
        return sector_value / max(total_value, account_size * 0.1)  # Avoid division by zero
    
    def _calculate_safety_score(self, checks: List[SafetyCheck]) -> float:
        """Calculate overall safety score (0-100)"""
        
        score = 100.0
        
        for check in checks:
            if check.status == SafetyLevel.BLOCKED:
                score -= 30
            elif check.status == SafetyLevel.DANGER:
                score -= 20
            elif check.status == SafetyLevel.WARNING:
                score -= 10
            elif check.status == SafetyLevel.CAUTION:
                score -= 5
        
        return max(score, 0.0)
    
    def _determine_overall_status(self, checks: List[SafetyCheck], blocking_issues: List[str]) -> SafetyLevel:
        """Determine overall safety status"""
        
        if len(blocking_issues) > 0:
            return SafetyLevel.BLOCKED
        
        danger_count = sum(1 for check in checks if check.status == SafetyLevel.DANGER)
        warning_count = sum(1 for check in checks if check.status == SafetyLevel.WARNING)
        caution_count = sum(1 for check in checks if check.status == SafetyLevel.CAUTION)
        
        if danger_count > 0:
            return SafetyLevel.DANGER
        elif warning_count > 1:
            return SafetyLevel.WARNING
        elif warning_count > 0 or caution_count > 2:
            return SafetyLevel.CAUTION
        else:
            return SafetyLevel.SAFE
    
    def _generate_educational_summary(self, status: SafetyLevel, score: float,
                                    blocking_count: int, warning_count: int) -> str:
        """Generate educational summary of safety assessment"""
        
        if status == SafetyLevel.BLOCKED:
            return f"""
🚫 TRADING BLOCKED - Safety Score: {score:.0f}/100

{blocking_count} critical safety issue(s) detected. Trading is suspended for your protection.

💡 EDUCATIONAL NOTE:
Safety guardrails are like seatbelts in a car - they might seem restrictive, but they protect you from serious harm. These blocks prevent potentially devastating losses that could wipe out your account.

🎯 NEXT STEPS:
1. Review and address the blocking issues
2. Wait for better market conditions if needed
3. Consider reducing position sizes or risk levels
4. Remember: Missing one trade is better than losing your account
            """.strip()
        
        elif status == SafetyLevel.WARNING:
            return f"""
⚠️ PROCEED WITH CAUTION - Safety Score: {score:.0f}/100

{warning_count} warning(s) detected. Trading is allowed but extra caution is recommended.

💡 EDUCATIONAL NOTE:
These warnings are like yellow traffic lights - you can proceed, but you should slow down and be extra careful. Consider reducing your position size or waiting for a better setup.

🎯 RECOMMENDATIONS:
1. Reduce position size by 25-50%
2. Use tighter stop-losses
3. Monitor positions more closely
4. Consider waiting for clearer signals
            """.strip()
        
        else:
            return f"""
✅ SAFE TO TRADE - Safety Score: {score:.0f}/100

All major safety checks passed. Conditions are favorable for trading.

💡 EDUCATIONAL NOTE:
Green lights don't guarantee success, but they indicate that the major risk factors are under control. Still use proper position sizing and risk management on every trade.

🎯 BEST PRACTICES:
1. Stick to your predetermined position size
2. Set stop-losses immediately after entry
3. Monitor the trade but don't over-manage
4. Stay disciplined with your trading plan
            """.strip()
