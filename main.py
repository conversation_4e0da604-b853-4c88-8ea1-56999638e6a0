"""
A.T.L.A.S AI Trading System - Main Entry Point
Advanced Trading & Learning Analysis System with Chain-of-Thought Intelligence
"""

import sys
import os

# Add streamlined directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'streamlined'))

if __name__ == "__main__":
    print("🧠 Starting A.T.L.A.S AI Trading System...")
    print("🎯 Advanced Trading & Learning Analysis System")
    print("📚 Chain-of-Thought Intelligence Active")
    print("🛡️ Safety Guardrails Enabled")
    print()
    print("🚀 Starting server...")
    
    # Import and run the streamlined atlas server
    import uvicorn

    uvicorn.run(
        "streamlined.atlas_server:app",
        host="0.0.0.0",
        port=8080,
        reload=True,
        log_level="info"
    )
