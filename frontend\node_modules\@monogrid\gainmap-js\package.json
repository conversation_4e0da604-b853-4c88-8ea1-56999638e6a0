{"name": "@monogrid/gainmap-js", "version": "3.1.0", "description": "A Javascript (TypeScript) Port of Adobe Gainmap Technology for storing HDR Images using an SDR Image + a gain map", "homepage": "https://github.com/MONOGRID/gainmap-js#readme", "repository": {"type": "git", "url": "git+ssh://**************/MONOGRID/gainmap-js.git"}, "keywords": ["hdr", "gain map", "gainmap", "three", "threejs"], "author": "MONOGRID <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/MONOGRID/gainmap-js/issues"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "main": "dist/decode.umd.cjs", "module": "dist/decode.js", "types": "dist/decode.d.ts", "sideEffects": false, "type": "module", "exports": {".": {"types": "./dist/decode.d.ts", "import": "./dist/decode.js", "default": "./dist/decode.umd.cjs"}, "./encode": {"types": "./dist/encode.d.ts", "import": "./dist/encode.js", "default": "./dist/encode.umd.cjs"}, "./libultrahdr": {"types": "./dist/libultrahdr.d.ts", "import": "./dist/libultrahdr.js", "default": "./dist/libultrahdr.umd.cjs"}, "./worker": {"types": "./dist/worker.d.ts", "import": "./dist/worker.js", "default": "./dist/worker.umd.cjs"}, "./worker-interface": {"types": "./dist/worker-interface.d.ts", "import": "./dist/worker-interface.js", "default": "./dist/worker-interface.umd.cjs"}}, "typesVersions": {"*": {".": ["./dist/decode.d.ts"], "encode": ["./dist/encode.d.ts"], "libultrahdr": ["./dist/libultrahdr.d.ts"], "worker": ["./dist/worker.d.ts"], "worker-interface": ["./dist/worker-interface.d.ts"]}}, "scripts": {"build": "rollup -c", "dev": "concurrently -n rollup,servez -c magenta,green \"rollup -c -w\" \"servez\"", "start": "concurrently -n rollup,servez -c magenta,green \"rollup -c -w\" \"servez\"", "test": "nyc --reporter=text --reporter=lcov playwright test", "test:startserver": "rollup -c && servez", "test:docker": "docker run --rm --network host -v $(pwd):/work -w /work -it -u $(id -u ${USER}):$(id -g ${USER}) mcr.microsoft.com/playwright:v1.46.0-jammy /bin/bash", "test:codegen": "playwright codegen http://localhost:8080", "prepack": "npm run build", "check": "concurrently -c auto npm:check:*", "check:typecheck-src": "tsc -p src", "check:eslint-src": "eslint \"src/**/*.ts\"", "check:typecheck-examples": "tsc -p examples", "check:eslint-examples": "eslint \"examples/**/*.{ts,html}\"", "check:typecheck-tests": "tsc -p tests", "check:eslint-tests": "eslint \"tests/**/*.ts\"", "ci:check": "concurrently npm:ci:check:*", "ci:check:typecheck-src": "tsc --pretty false -p src > reports/typecheck.log", "ci:check:eslint-src": "eslint --format json --output-file reports/eslint-src.json \"src/**/*.ts\"", "ci:check:typecheck-examples": "tsc --pretty false -p examples > reports/typecheck-examples.log", "ci:check:eslint-examples": "eslint --format json --output-file reports/eslint-examples.json \"examples/**/*.{ts,html}\"", "ci:check:typecheck-tests": "tsc --pretty false -p tests > reports/typecheck-tests.log", "ci:check:eslint-tests": "eslint --format json --output-file reports/eslint-tests.json \"tests/**/*.ts\""}, "peerDependencies": {"three": ">= 0.159.0"}, "dependencies": {"promise-worker-transferable": "^1.0.4"}, "devDependencies": {"@playwright/test": "^1.46.0", "@rollup/plugin-commonjs": "^26.0.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.6", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.0", "@semantic-release/git": "github:semantic-release/git", "@semantic-release/github": "^10.1.3", "@semantic-release/npm": "^12.0.1", "@semantic-release/release-notes-generator": "^14.0.1", "@types/node": "^20.14.9", "@types/three": "^0.170.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "concurrently": "^8.2.2", "conventional-changelog-conventionalcommits": "^8.0.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.56.0", "eslint-config-mdcs": "^5.0.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-compat": "^4.2.0", "eslint-plugin-html": "^8.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-promise": "^6.4.0", "eslint-plugin-simple-import-sort": "^12.0.0", "eslint-plugin-unused-imports": "^3.2.0", "nyc": "^17.0.0", "rollup": "^4.20.0", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-delete": "^2.0.0", "rollup-plugin-istanbul": "^5.0.0", "rollup-plugin-license": "^3.5.2", "semantic-release": "^24.0.0", "servez": "^2.2.3", "sharp": "^0.33.4", "three": "^0.170.0", "typedoc": "^0.26.5", "typedoc-github-wiki-theme": "^2.0.0", "typedoc-plugin-markdown": "^4.2.3", "typescript": "^5.5.4"}, "browserslist": ["> 1%, not dead, not ie 11, not op_mini all"]}