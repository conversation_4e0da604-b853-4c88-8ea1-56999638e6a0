{"version": 3, "sources": ["../src/index.ts", "../src/applyAnimatedValues.ts", "../src/AnimatedStyle.ts", "../src/primitives.ts"], "sourcesContent": ["import { Globals } from '@react-spring/core'\nimport { unstable_batchedUpdates } from 'react-dom'\nimport { createStringInterpolator, colors } from '@react-spring/shared'\nimport { createHost } from '@react-spring/animated'\nimport { applyAnimatedValues } from './applyAnimatedValues'\nimport { AnimatedStyle } from './AnimatedStyle'\nimport { WithAnimated } from './animated'\nimport { primitives } from './primitives'\n\nGlobals.assign({\n  batchedUpdates: unstable_batchedUpdates,\n  createStringInterpolator,\n  colors,\n})\n\nconst host = createHost(primitives, {\n  applyAnimatedValues,\n  createAnimatedStyle: style => new AnimatedStyle(style),\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getComponentProps: ({ scrollTop, scrollLeft, ...props }) => props,\n})\n\nexport const animated = host.animated as WithAnimated\nexport { animated as a }\n\nexport * from './animated'\nexport * from '@react-spring/core'\n", "import { Lookup } from '@react-spring/types'\n\nconst isCustomPropRE = /^--/\n\ntype Value = string | number | boolean | null\n\nfunction dangerousStyleValue(name: string, value: Value) {\n  if (value == null || typeof value === 'boolean' || value === '') return ''\n  if (\n    typeof value === 'number' &&\n    value !== 0 &&\n    !isCustomPropRE.test(name) &&\n    !(isUnitlessNumber.hasOwnProperty(name) && isUnitlessNumber[name])\n  )\n    return value + 'px'\n  // Presumes implicit 'px' suffix for unitless numbers\n  return ('' + value).trim()\n}\n\nconst attributeCache: Lookup<string> = {}\n\ntype Instance = HTMLDivElement & { style?: Lookup }\n\nexport function applyAnimatedValues(instance: Instance, props: Lookup) {\n  if (!instance.nodeType || !instance.setAttribute) {\n    return false\n  }\n\n  const isFilterElement =\n    instance.nodeName === 'filter' ||\n    (instance.parentNode && instance.parentNode.nodeName === 'filter')\n\n  const {\n    className,\n    style,\n    children,\n    scrollTop,\n    scrollLeft,\n    viewBox,\n    ...attributes\n  } = props!\n\n  const values = Object.values(attributes)\n  const names = Object.keys(attributes).map(name =>\n    isFilterElement || instance.hasAttribute(name)\n      ? name\n      : attributeCache[name] ||\n        (attributeCache[name] = name.replace(\n          /([A-Z])/g,\n          // Attributes are written in dash case\n          n => '-' + n.toLowerCase()\n        ))\n  )\n\n  if (children !== void 0) {\n    instance.textContent = children\n  }\n\n  // Apply CSS styles\n  for (const name in style) {\n    if (style.hasOwnProperty(name)) {\n      const value = dangerousStyleValue(name, style[name])\n      if (isCustomPropRE.test(name)) {\n        instance.style.setProperty(name, value)\n      } else {\n        instance.style[name] = value\n      }\n    }\n  }\n\n  // Apply DOM attributes\n  names.forEach((name, i) => {\n    instance.setAttribute(name, values[i])\n  })\n\n  if (className !== void 0) {\n    instance.className = className\n  }\n  if (scrollTop !== void 0) {\n    instance.scrollTop = scrollTop\n  }\n  if (scrollLeft !== void 0) {\n    instance.scrollLeft = scrollLeft\n  }\n  if (viewBox !== void 0) {\n    instance.setAttribute('viewBox', viewBox)\n  }\n}\n\nlet isUnitlessNumber: { [key: string]: true } = {\n  animationIterationCount: true,\n  borderImageOutset: true,\n  borderImageSlice: true,\n  borderImageWidth: true,\n  boxFlex: true,\n  boxFlexGroup: true,\n  boxOrdinalGroup: true,\n  columnCount: true,\n  columns: true,\n  flex: true,\n  flexGrow: true,\n  flexPositive: true,\n  flexShrink: true,\n  flexNegative: true,\n  flexOrder: true,\n  gridRow: true,\n  gridRowEnd: true,\n  gridRowSpan: true,\n  gridRowStart: true,\n  gridColumn: true,\n  gridColumnEnd: true,\n  gridColumnSpan: true,\n  gridColumnStart: true,\n  fontWeight: true,\n  lineClamp: true,\n  lineHeight: true,\n  opacity: true,\n  order: true,\n  orphans: true,\n  tabSize: true,\n  widows: true,\n  zIndex: true,\n  zoom: true,\n  // SVG-related properties\n  fillOpacity: true,\n  floodOpacity: true,\n  stopOpacity: true,\n  strokeDasharray: true,\n  strokeDashoffset: true,\n  strokeMiterlimit: true,\n  strokeOpacity: true,\n  strokeWidth: true,\n}\n\nconst prefixKey = (prefix: string, key: string) =>\n  prefix + key.charAt(0).toUpperCase() + key.substring(1)\nconst prefixes = ['Webkit', 'Ms', 'Moz', 'O']\n\nisUnitlessNumber = Object.keys(isUnitlessNumber).reduce((acc, prop) => {\n  prefixes.forEach(prefix => (acc[prefixKey(prefix, prop)] = acc[prop]))\n  return acc\n}, isUnitlessNumber)\n", "import { AnimatedObject } from '@react-spring/animated'\nimport { Lookup, OneOrMore } from '@react-spring/types'\nimport {\n  is,\n  each,\n  toArray,\n  eachProp,\n  FluidValue,\n  FluidEvent,\n  getFluidValue,\n  callFluidObservers,\n  hasFluidValue,\n  addFluidObserver,\n  removeFluidObserver,\n} from '@react-spring/shared'\n\n/** The transform-functions\n * (https://developer.mozilla.org/fr/docs/Web/CSS/transform-function)\n * that you can pass as keys to your animated component style and that will be\n * animated. Perspective has been left out as it would conflict with the\n * non-transform perspective style.\n */\nconst domTransforms = /^(matrix|translate|scale|rotate|skew)/\n\n// These keys have \"px\" units by default\nconst pxTransforms = /^(translate)/\n\n// These keys have \"deg\" units by default\nconst degTransforms = /^(rotate|skew)/\n\ntype Value = number | string\n\n/** Add a unit to the value when the value is unit-less (eg: a number) */\nconst addUnit = (value: Value, unit: string): string | 0 =>\n  is.num(value) && value !== 0 ? value + unit : value\n\n/**\n * Checks if the input value matches the identity value.\n *\n *     isValueIdentity(0, 0)              // => true\n *     isValueIdentity('0px', 0)          // => true\n *     isValueIdentity([0, '0px', 0], 0)  // => true\n */\nconst isValueIdentity = (value: OneOrMore<Value>, id: number): boolean =>\n  is.arr(value)\n    ? value.every(v => isValueIdentity(v, id))\n    : is.num(value)\n      ? value === id\n      : parseFloat(value) === id\n\ntype Inputs = ReadonlyArray<Value | FluidValue<Value>>[]\ntype Transforms = ((value: any) => [string, boolean])[]\n\n/**\n * This AnimatedStyle will simplify animated components transforms by\n * interpolating all transform function passed as keys in the style object\n * including shortcuts such as x, y and z for translateX/Y/Z\n */\nexport class AnimatedStyle extends AnimatedObject {\n  constructor({ x, y, z, ...style }: Lookup) {\n    /**\n     * An array of arrays that contains the values (static or fluid)\n     * used by each transform function.\n     */\n    const inputs: Inputs = []\n    /**\n     * An array of functions that take a list of values (static or fluid)\n     * and returns (1) a CSS transform string and (2) a boolean that's true\n     * when the transform has no effect (eg: an identity transform).\n     */\n    const transforms: Transforms = []\n\n    // Combine x/y/z into translate3d\n    if (x || y || z) {\n      inputs.push([x || 0, y || 0, z || 0])\n      transforms.push((xyz: Value[]) => [\n        `translate3d(${xyz.map(v => addUnit(v, 'px')).join(',')})`, // prettier-ignore\n        isValueIdentity(xyz, 0),\n      ])\n    }\n\n    // Pluck any other transform-related props\n    eachProp(style, (value, key) => {\n      if (key === 'transform') {\n        inputs.push([value || ''])\n        transforms.push((transform: string) => [transform, transform === ''])\n      } else if (domTransforms.test(key)) {\n        delete style[key]\n        if (is.und(value)) return\n\n        const unit = pxTransforms.test(key)\n          ? 'px'\n          : degTransforms.test(key)\n            ? 'deg'\n            : ''\n\n        inputs.push(toArray(value))\n        transforms.push(\n          key === 'rotate3d'\n            ? ([x, y, z, deg]: [number, number, number, Value]) => [\n                `rotate3d(${x},${y},${z},${addUnit(deg, unit)})`,\n                isValueIdentity(deg, 0),\n              ]\n            : (input: Value[]) => [\n                `${key}(${input.map(v => addUnit(v, unit)).join(',')})`,\n                isValueIdentity(input, key.startsWith('scale') ? 1 : 0),\n              ]\n        )\n      }\n    })\n\n    if (inputs.length) {\n      style.transform = new FluidTransform(inputs, transforms)\n    }\n\n    super(style)\n  }\n}\n\n/** @internal */\nclass FluidTransform extends FluidValue<string> {\n  protected _value: string | null = null\n\n  constructor(\n    readonly inputs: Inputs,\n    readonly transforms: Transforms\n  ) {\n    super()\n  }\n\n  get() {\n    return this._value || (this._value = this._get())\n  }\n\n  protected _get() {\n    let transform = ''\n    let identity = true\n    each(this.inputs, (input, i) => {\n      const arg1 = getFluidValue(input[0])\n      const [t, id] = this.transforms[i](\n        is.arr(arg1) ? arg1 : input.map(getFluidValue)\n      )\n      transform += ' ' + t\n      identity = identity && id\n    })\n    return identity ? 'none' : transform\n  }\n\n  // Start observing our inputs once we have an observer.\n  protected observerAdded(count: number) {\n    if (count == 1)\n      each(this.inputs, input =>\n        each(\n          input,\n          value => hasFluidValue(value) && addFluidObserver(value, this)\n        )\n      )\n  }\n\n  // Stop observing our inputs once we have no observers.\n  protected observerRemoved(count: number) {\n    if (count == 0)\n      each(this.inputs, input =>\n        each(\n          input,\n          value => hasFluidValue(value) && removeFluidObserver(value, this)\n        )\n      )\n  }\n\n  eventObserved(event: FluidEvent) {\n    if (event.type == 'change') {\n      this._value = null\n    }\n    callFluidObservers(this, event)\n  }\n}\n", "export type Primitives = keyof JSX.IntrinsicElements\nexport const primitives: Primitives[] = [\n  'a',\n  'abbr',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'base',\n  'bdi',\n  'bdo',\n  'big',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'data',\n  'datalist',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'embed',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'keygen',\n  'label',\n  'legend',\n  'li',\n  'link',\n  'main',\n  'map',\n  'mark',\n  'menu',\n  'menuitem',\n  'meta',\n  'meter',\n  'nav',\n  'noscript',\n  'object',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'param',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'script',\n  'section',\n  'select',\n  'small',\n  'source',\n  'span',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'title',\n  'tr',\n  'track',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n  // SVG\n  'circle',\n  'clipPath',\n  'defs',\n  'ellipse',\n  'foreignObject',\n  'g',\n  'image',\n  'line',\n  'linearGradient',\n  'mask',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialGradient',\n  'rect',\n  'stop',\n  'svg',\n  'text',\n  'tspan',\n]\n"], "mappings": ";AAAA,SAAS,eAAe;AACxB,SAAS,+BAA+B;AACxC,SAAS,0BAA0B,cAAc;AACjD,SAAS,kBAAkB;;;ACD3B,IAAM,iBAAiB;AAIvB,SAAS,oBAAoB,MAAc,OAAc;AACvD,MAAI,SAAS,QAAQ,OAAO,UAAU,aAAa,UAAU;AAAI,WAAO;AACxE,MACE,OAAO,UAAU,YACjB,UAAU,KACV,CAAC,eAAe,KAAK,IAAI,KACzB,EAAE,iBAAiB,eAAe,IAAI,KAAK,iBAAiB,IAAI;AAEhE,WAAO,QAAQ;AAEjB,UAAQ,KAAK,OAAO,KAAK;AAC3B;AAEA,IAAM,iBAAiC,CAAC;AAIjC,SAAS,oBAAoB,UAAoB,OAAe;AACrE,MAAI,CAAC,SAAS,YAAY,CAAC,SAAS,cAAc;AAChD,WAAO;AAAA,EACT;AAEA,QAAM,kBACJ,SAAS,aAAa,YACrB,SAAS,cAAc,SAAS,WAAW,aAAa;AAE3D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AAEJ,QAAM,SAAS,OAAO,OAAO,UAAU;AACvC,QAAM,QAAQ,OAAO,KAAK,UAAU,EAAE;AAAA,IAAI,UACxC,mBAAmB,SAAS,aAAa,IAAI,IACzC,OACA,eAAe,IAAI,MAClB,eAAe,IAAI,IAAI,KAAK;AAAA,MAC3B;AAAA;AAAA,MAEA,OAAK,MAAM,EAAE,YAAY;AAAA,IAC3B;AAAA,EACN;AAEA,MAAI,aAAa,QAAQ;AACvB,aAAS,cAAc;AAAA,EACzB;AAGA,aAAW,QAAQ,OAAO;AACxB,QAAI,MAAM,eAAe,IAAI,GAAG;AAC9B,YAAM,QAAQ,oBAAoB,MAAM,MAAM,IAAI,CAAC;AACnD,UAAI,eAAe,KAAK,IAAI,GAAG;AAC7B,iBAAS,MAAM,YAAY,MAAM,KAAK;AAAA,MACxC,OAAO;AACL,iBAAS,MAAM,IAAI,IAAI;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAGA,QAAM,QAAQ,CAAC,MAAM,MAAM;AACzB,aAAS,aAAa,MAAM,OAAO,CAAC,CAAC;AAAA,EACvC,CAAC;AAED,MAAI,cAAc,QAAQ;AACxB,aAAS,YAAY;AAAA,EACvB;AACA,MAAI,cAAc,QAAQ;AACxB,aAAS,YAAY;AAAA,EACvB;AACA,MAAI,eAAe,QAAQ;AACzB,aAAS,aAAa;AAAA,EACxB;AACA,MAAI,YAAY,QAAQ;AACtB,aAAS,aAAa,WAAW,OAAO;AAAA,EAC1C;AACF;AAEA,IAAI,mBAA4C;AAAA,EAC9C,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,SAAS;AAAA,EACT,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,WAAW;AAAA,EACX,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA;AAAA,EAEN,aAAa;AAAA,EACb,cAAc;AAAA,EACd,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,aAAa;AACf;AAEA,IAAM,YAAY,CAAC,QAAgB,QACjC,SAAS,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,UAAU,CAAC;AACxD,IAAM,WAAW,CAAC,UAAU,MAAM,OAAO,GAAG;AAE5C,mBAAmB,OAAO,KAAK,gBAAgB,EAAE,OAAO,CAAC,KAAK,SAAS;AACrE,WAAS,QAAQ,YAAW,IAAI,UAAU,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAE;AACrE,SAAO;AACT,GAAG,gBAAgB;;;AC7InB,SAAS,sBAAsB;AAE/B;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AAQP,IAAM,gBAAgB;AAGtB,IAAM,eAAe;AAGrB,IAAM,gBAAgB;AAKtB,IAAM,UAAU,CAAC,OAAc,SAC7B,GAAG,IAAI,KAAK,KAAK,UAAU,IAAI,QAAQ,OAAO;AAShD,IAAM,kBAAkB,CAAC,OAAyB,OAChD,GAAG,IAAI,KAAK,IACR,MAAM,MAAM,OAAK,gBAAgB,GAAG,EAAE,CAAC,IACvC,GAAG,IAAI,KAAK,IACV,UAAU,KACV,WAAW,KAAK,MAAM;AAUvB,IAAM,gBAAN,cAA4B,eAAe;AAAA,EAChD,YAAY,EAAE,GAAG,GAAG,GAAG,GAAG,MAAM,GAAW;AAKzC,UAAM,SAAiB,CAAC;AAMxB,UAAM,aAAyB,CAAC;AAGhC,QAAI,KAAK,KAAK,GAAG;AACf,aAAO,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC;AACpC,iBAAW,KAAK,CAAC,QAAiB;AAAA,QAChC,eAAe,IAAI,IAAI,OAAK,QAAQ,GAAG,IAAI,CAAC,EAAE,KAAK,GAAG;AAAA;AAAA,QACtD,gBAAgB,KAAK,CAAC;AAAA,MACxB,CAAC;AAAA,IACH;AAGA,aAAS,OAAO,CAAC,OAAO,QAAQ;AAC9B,UAAI,QAAQ,aAAa;AACvB,eAAO,KAAK,CAAC,SAAS,EAAE,CAAC;AACzB,mBAAW,KAAK,CAAC,cAAsB,CAAC,WAAW,cAAc,EAAE,CAAC;AAAA,MACtE,WAAW,cAAc,KAAK,GAAG,GAAG;AAClC,eAAO,MAAM,GAAG;AAChB,YAAI,GAAG,IAAI,KAAK;AAAG;AAEnB,cAAM,OAAO,aAAa,KAAK,GAAG,IAC9B,OACA,cAAc,KAAK,GAAG,IACpB,QACA;AAEN,eAAO,KAAK,QAAQ,KAAK,CAAC;AAC1B,mBAAW;AAAA,UACT,QAAQ,aACJ,CAAC,CAACA,IAAGC,IAAGC,IAAG,GAAG,MAAuC;AAAA,YACnD,YAAYF,MAAKC,MAAKC,MAAK,QAAQ,KAAK,IAAI;AAAA,YAC5C,gBAAgB,KAAK,CAAC;AAAA,UACxB,IACA,CAAC,UAAmB;AAAA,YAClB,GAAG,OAAO,MAAM,IAAI,OAAK,QAAQ,GAAG,IAAI,CAAC,EAAE,KAAK,GAAG;AAAA,YACnD,gBAAgB,OAAO,IAAI,WAAW,OAAO,IAAI,IAAI,CAAC;AAAA,UACxD;AAAA,QACN;AAAA,MACF;AAAA,IACF,CAAC;AAED,QAAI,OAAO,QAAQ;AACjB,YAAM,YAAY,IAAI,eAAe,QAAQ,UAAU;AAAA,IACzD;AAEA,UAAM,KAAK;AAAA,EACb;AACF;AAGA,IAAM,iBAAN,cAA6B,WAAmB;AAAA,EAG9C,YACW,QACA,YACT;AACA,UAAM;AAHG;AACA;AAJX,SAAU,SAAwB;AAAA,EAOlC;AAAA,EAEA,MAAM;AACJ,WAAO,KAAK,WAAW,KAAK,SAAS,KAAK,KAAK;AAAA,EACjD;AAAA,EAEU,OAAO;AACf,QAAI,YAAY;AAChB,QAAI,WAAW;AACf,SAAK,KAAK,QAAQ,CAAC,OAAO,MAAM;AAC9B,YAAM,OAAO,cAAc,MAAM,CAAC,CAAC;AACnC,YAAM,CAAC,GAAG,EAAE,IAAI,KAAK,WAAW,CAAC;AAAA,QAC/B,GAAG,IAAI,IAAI,IAAI,OAAO,MAAM,IAAI,aAAa;AAAA,MAC/C;AACA,mBAAa,MAAM;AACnB,iBAAW,YAAY;AAAA,IACzB,CAAC;AACD,WAAO,WAAW,SAAS;AAAA,EAC7B;AAAA;AAAA,EAGU,cAAc,OAAe;AACrC,QAAI,SAAS;AACX;AAAA,QAAK,KAAK;AAAA,QAAQ,WAChB;AAAA,UACE;AAAA,UACA,WAAS,cAAc,KAAK,KAAK,iBAAiB,OAAO,IAAI;AAAA,QAC/D;AAAA,MACF;AAAA,EACJ;AAAA;AAAA,EAGU,gBAAgB,OAAe;AACvC,QAAI,SAAS;AACX;AAAA,QAAK,KAAK;AAAA,QAAQ,WAChB;AAAA,UACE;AAAA,UACA,WAAS,cAAc,KAAK,KAAK,oBAAoB,OAAO,IAAI;AAAA,QAClE;AAAA,MACF;AAAA,EACJ;AAAA,EAEA,cAAc,OAAmB;AAC/B,QAAI,MAAM,QAAQ,UAAU;AAC1B,WAAK,SAAS;AAAA,IAChB;AACA,uBAAmB,MAAM,KAAK;AAAA,EAChC;AACF;;;AC/KO,IAAM,aAA2B;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AH9GA,cAAc;AAjBd,QAAQ,OAAO;AAAA,EACb,gBAAgB;AAAA,EAChB;AAAA,EACA;AACF,CAAC;AAED,IAAM,OAAO,WAAW,YAAY;AAAA,EAClC;AAAA,EACA,qBAAqB,WAAS,IAAI,cAAc,KAAK;AAAA;AAAA,EAErD,mBAAmB,CAAC,EAAE,WAAW,YAAY,GAAG,MAAM,MAAM;AAC9D,CAAC;AAEM,IAAM,WAAW,KAAK;", "names": ["x", "y", "z"]}